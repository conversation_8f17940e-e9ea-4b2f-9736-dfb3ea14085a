# 🚀 **SERVIDOR INTEGRADO - VEREADORA RAFAELA**

## ✨ **UM ÚNICO COMANDO PARA TUDO**

O backend da Vereadora Rafaela agora é **completamente integrado**! 
Não é mais necessário executar múltiplos comandos ou gerenciar processos separados.

---

## 🎯 **COMO USAR**

### **1. Configuração Inicial**
```bash
# Instalar dependências
npm install

# Configurar ambiente (opcional)
cp .env.example .env
```

### **2. Executar o Sistema**

#### **Desenvolvimento**
```bash
npm run dev
```

#### **Produção**
```bash
npm start
```

**É isso! Um único comando inicia:**
- ✅ Servidor Backend (porta 3001)
- ✅ WPPConnect Server integrado
- ✅ Sistema RAG completo
- ✅ Todos os serviços WhatsApp
- ✅ Monitoramento e logs

---

## 🔧 **O QUE ESTÁ INTEGRADO**

### **Serviços Automáticos**
- **WhatsApp Service**: Gerenciamento completo do WhatsApp
- **WPPConnect Server**: Servidor WPPConnect embutido
- **RAG Service**: Sistema de IA e recuperação de documentos
- **Anti-Ban Service**: Proteção contra bloqueios
- **Session Manager**: Gerenciamento de sessões
- **Message Handler**: Processamento inteligente de mensagens

### **Recursos Inclusos**
- **QR Code automático**: Gerado e servido automaticamente
- **Sincronização de chats**: Automática após conexão
- **Fallback inteligente**: Se um serviço falha, outros continuam
- **Logs centralizados**: Todos os logs em um local
- **Health checks**: Monitoramento contínuo

---

## 📊 **MONITORAMENTO**

### **Status dos Serviços**
```bash
# Verificar status
npm run services:status

# Health check
npm run services:health
```

### **Endpoints de Monitoramento**
- `GET /api/services/status` - Status de todos os serviços
- `GET /api/services/health` - Health check completo
- `GET /api/whatsapp/status` - Status específico do WhatsApp

---

## 🔧 **CONFIGURAÇÕES PRINCIPAIS**

### **Variáveis de Ambiente (.env)**
```env
# Servidor
PORT=3001
NODE_ENV=development

# WhatsApp
WHATSAPP_USE_HTTP=true
WHATSAPP_SESSION_NAME=vereadora-rafaela

# Gemini AI (opcional)
GEMINI_API_KEY=sua_chave_aqui

# Supabase (opcional)
SUPABASE_URL=sua_url_aqui
SUPABASE_ANON_KEY=sua_chave_aqui
```

---

## 🚨 **TROUBLESHOOTING**

### **Problema: Servidor não inicia**
```bash
# Verificar sintaxe
npm run validate

# Ver logs detalhados
npm run dev
```

### **Problema: WhatsApp não conecta**
- O QR code será exibido automaticamente nos logs
- Acesse `http://localhost:3001/api/whatsapp/qr` para ver o QR
- O sistema tentará reconectar automaticamente

### **Problema: Serviços falham**
- O servidor continua funcionando em modo degradado
- Verifique os logs para detalhes específicos
- Serviços são reiniciados automaticamente quando possível

---

## 📚 **COMANDOS DISPONÍVEIS**

```bash
# Desenvolvimento com hot-reload
npm run dev

# Produção
npm start

# Validar configuração
npm run validate

# Verificar status dos serviços
npm run services:status

# Health check completo
npm run services:health

# Setup inicial
npm run setup
```

---

## 🎉 **VANTAGENS DA INTEGRAÇÃO**

- ✅ **Simplicidade**: Um único comando para tudo
- ✅ **Confiabilidade**: Fallbacks automáticos
- ✅ **Monitoramento**: Status centralizado
- ✅ **Manutenção**: Logs unificados
- ✅ **Performance**: Otimizado para produção
- ✅ **Escalabilidade**: Arquitetura modular

---

## 📞 **SUPORTE**

Para problemas ou dúvidas:
1. Verifique os logs: `npm run dev`
2. Consulte o status: `npm run services:status`
3. Valide a configuração: `npm run validate`

**O sistema foi projetado para funcionar de forma autônoma e resiliente!** 🚀
