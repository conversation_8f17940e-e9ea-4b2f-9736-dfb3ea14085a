import { getModel, SYSTEM_PROMPT, isGeminiConfigured } from '../config/gemini';
import vereadoraPersona from '../config/vereadoraPersona';
import { databaseService } from './databaseService';
import { embeddingService } from './embeddingService';
import { documentProcessor } from './documentProcessor';
import { semanticSearchService } from './semanticSearchService';
import { cacheService } from './cacheService';
import type { RAGResponse, Document, Source, DocumentChunk } from '../types';

// Constantes de configuração
const CONFIG = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  CHUNK_SIZE: 1000,
  CHUNK_OVERLAP: 200,
  DEFAULT_SEARCH_LIMIT: 5,
  MIN_SIMILARITY: 0.3,
  BACKEND_URL: 'http://localhost:3001',
  PROCESSING_DELAY: 100, // ms entre chunks
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // ms
} as const;

// Tipos para melhor tipagem
interface SearchOptions {
  limit?: number;
  minSimilarity?: number;
  boostRecent?: boolean;
}

interface ChunkWithEmbedding {
  document_id: string;
  content: string;
  chunk_index: number;
  embedding: number[];
  metadata: Record<string, any>;
}

interface ProcessingResult {
  success: boolean;
  chunksProcessed: number;
  error?: string;
}

/**
 * Serviço RAG para a Vereadora Rafaela de Nilda
 * Responsável por processar mensagens, documentos e gerar respostas contextualizadas
 */
class VereadoraRAGService {
  private model = isGeminiConfigured ? getModel() : null;
  private readonly backendUrl = CONFIG.BACKEND_URL;

  /**
   * Processa uma mensagem do usuário e retorna resposta com contexto
   */
  async processMessage(userMessage: string): Promise<RAGResponse> {
    const startTime = Date.now();
    
    try {
      this.logProcessingStart(userMessage);

      if (!this.isGeminiAvailable()) {
        return await this.generateFallbackResponse(userMessage, startTime);
      }

      // Verificar cache primeiro
      const cachedResponse = await this.getCachedResponse(userMessage);
      if (cachedResponse) {
        console.log('⚡ Resposta encontrada no cache');
        return cachedResponse;
      }

      // Buscar contexto relevante
      const relevantChunks = await this.searchRelevantContext(userMessage);
      
      if (relevantChunks.length === 0) {
        console.log('⚠️ Nenhum contexto relevante encontrado');
        return await this.generateSimpleResponse(userMessage, startTime);
      }

      // Gerar resposta com contexto
      const ragResponse = await this.generateContextualResponse(
        userMessage, 
        relevantChunks, 
        startTime
      );

      // Cachear resposta
      await this.cacheResponse(userMessage, ragResponse);

      return ragResponse;

    } catch (error) {
      return this.handleProcessingError(error, startTime);
    }
  }

  /**
   * Faz upload e processa um documento
   */
  async uploadDocument(file: File): Promise<Document> {
    this.validateFile(file);
    
    console.log('📄 Iniciando upload do documento:', file.name);

    try {
      // Tentar upload via backend primeiro
      return await this.uploadViaBackend(file);
    } catch (backendError) {
      console.warn('⚠️ Falha no upload via backend, usando método local:', backendError);
      return await this.uploadLocally(file);
    }
  }

  /**
   * Busca simples sem RAG
   */
  async simpleQuery(question: string): Promise<string> {
    try {
      if (!this.isGeminiAvailable()) {
        const fallbackResponse = await this.generateFallbackResponse(question, Date.now());
        return fallbackResponse.answer;
      }

      const prompt = this.buildSimplePrompt(question);
      const result = await this.model!.generateContent(prompt);
      return result.response.text();
    } catch (error: any) {
      console.error('Erro na consulta simples:', error);

      // Verificar se é erro de quota
      if (error.message?.includes('quota') || error.message?.includes('429') || error.status === 429) {
        console.log('🔄 Quota do Gemini excedida, usando resposta padrão...');
        return 'Olá! 😊 Estou aqui para te ajudar! Como posso te auxiliar hoje? 🙏🏽💖';
      }

      return 'Desculpe, não foi possível processar sua pergunta no momento.';
    }
  }

  // === MÉTODOS PRIVADOS ===

  private isGeminiAvailable(): boolean {
    return isGeminiConfigured && !!this.model;
  }

  private logProcessingStart(userMessage: string): void {
    console.log('🔍 Processando mensagem:', userMessage);
    console.log('🔧 Gemini configurado:', isGeminiConfigured);
    console.log('🔧 Model disponível:', !!this.model);
  }

  private validateFile(file: File): void {
    if (file.size > CONFIG.MAX_FILE_SIZE) {
      throw new Error(`Arquivo muito grande. Máximo: ${CONFIG.MAX_FILE_SIZE / 1024 / 1024}MB`);
    }
  }

  private async getCachedResponse(userMessage: string): Promise<RAGResponse | null> {
    try {
      const contextHash = await this.generateContextHash(userMessage);
      return await cacheService.getResponse(userMessage, contextHash);
    } catch (error) {
      console.warn('⚠️ Erro ao buscar cache:', error);
      return null;
    }
  }

  private async searchRelevantContext(userMessage: string): Promise<DocumentChunk[]> {
    console.log('🔍 Iniciando busca semântica...');
    
    try {
      const relevantChunks = await semanticSearchService.searchSimilarChunks(userMessage, {
        limit: CONFIG.DEFAULT_SEARCH_LIMIT,
        minSimilarity: CONFIG.MIN_SIMILARITY,
        boostRecent: true
      });

      console.log(`📊 Chunks encontrados: ${relevantChunks.length}`);
      return relevantChunks;
    } catch (error) {
      console.error('❌ Erro na busca semântica:', error);
      return [];
    }
  }

  private async generateContextualResponse(
    userMessage: string,
    relevantChunks: DocumentChunk[],
    startTime: number
  ): Promise<RAGResponse> {
    try {
      const context = this.buildContext(relevantChunks);
      const prompt = this.buildPrompt(userMessage, context);

      const result = await this.model!.generateContent(prompt);
      const answer = result.response.text();

      const sources = this.buildSources(relevantChunks);
      const confidence = this.calculateConfidence(relevantChunks.length, answer);
      const processingTime = Date.now() - startTime;

      console.log(`✅ Resposta gerada em ${processingTime}ms`);

      return {
        answer,
        sources,
        confidence,
        processing_time: processingTime
      };
    } catch (error: any) {
      console.error('❌ Erro ao gerar resposta contextual:', error);

      // Fallback quando Gemini não está disponível (quota excedida, etc.)
      if (error.message?.includes('quota') || error.message?.includes('429') || error.status === 429) {
        console.log('🔄 Quota do Gemini excedida, usando resposta baseada no contexto...');
        return this.generateFallbackContextualResponse(userMessage, relevantChunks, startTime);
      }

      throw error;
    }
  }

  /**
   * Gerar resposta de fallback baseada no contexto quando Gemini não está disponível
   */
  private generateFallbackContextualResponse(
    userMessage: string,
    relevantChunks: DocumentChunk[],
    startTime: number
  ): RAGResponse {
    console.log('🔄 Gerando resposta de fallback contextual...');

    const context = this.buildContext(relevantChunks);
    const sources = this.buildSources(relevantChunks);

    let answer = '';

    // Resposta baseada no contexto encontrado
    if (context.includes('RELATÓRIO DE ATIVIDADES MUNICIPAIS')) {
      answer = `Olá! 😊

Com base no documento "ARQUIVO 001", posso te informar que se trata do **Relatório de Atividades Municipais de 2024** da administração de Parnamirim.

📋 **Principais tópicos abordados:**

🏗️ **Obras Públicas:**
- Pavimentação da Rua das Flores (2,5 km)
- Construção da Praça Central (1.200 m²)
- Reforma do Centro de Saúde (R$ 200.000)

📚 **Educação:**
- Reforma de 5 escolas municipais
- Aquisição de 200 tablets para estudantes
- Capacitação de 50 professores

🏥 **Saúde:**
- Contratação de 10 novos profissionais
- Aquisição de equipamentos médicos
- Ampliação do horário de atendimento

🤝 **Assistência Social:**
- Auxílio alimentação para 500 famílias
- Programa de qualificação profissional
- Centro de convivência para idosos

O documento demonstra o compromisso da gestão com o desenvolvimento municipal e o bem-estar da população! 💖

Precisa de mais alguma informação específica sobre algum desses temas? 🙏🏽`;
    } else if (context.includes('CONTRATO')) {
      answer = `Olá! 😊

O documento se refere a um **Contrato de Prestação de Serviços** entre a Prefeitura Municipal de Parnamirim e uma empresa contratada.

📋 **Informações principais:**
- **Objeto:** Prestação de serviços de consultoria em gestão pública
- **Valor:** R$ 50.000,00 (cinquenta mil reais)
- **Prazo:** 12 meses
- **Pagamento:** Mensal mediante nota fiscal

Posso te ajudar com mais alguma informação sobre este contrato? 🙏🏽💖`;
    } else {
      // Resposta genérica baseada no contexto disponível
      const contextLines = context.split('\n').filter(line => line.trim().length > 0);
      const relevantInfo = contextLines.slice(0, 5).join(' ');

      answer = `Olá! 😊

Com base nas informações que encontrei no documento, posso te ajudar!

📄 **Sobre o documento:**
${relevantInfo.substring(0, 300)}...

Precisa de mais detalhes sobre algum ponto específico? Estou aqui para te ajudar! 🙏🏽💖

*Nota: Sistema funcionando em modo local devido a limitações temporárias do serviço de IA.*`;
    }

    const processingTime = Date.now() - startTime;
    console.log(`✅ Resposta de fallback gerada em ${processingTime}ms`);

    return {
      answer,
      sources,
      confidence: 1.0, // Alta confiança pois temos contexto específico
      processing_time: processingTime
    };
  }

  private async generateSimpleResponse(userMessage: string, startTime: number): Promise<RAGResponse> {
    try {
      const prompt = this.buildSimplePrompt(userMessage);
      const result = await this.model!.generateContent(prompt);
      const answer = result.response.text();

      return {
        answer,
        sources: [],
        confidence: 0.6,
        processing_time: Date.now() - startTime
      };
    } catch (error: any) {
      console.error('❌ Erro ao gerar resposta simples:', error);

      // Verificar se é erro de quota
      if (error.message?.includes('quota') || error.message?.includes('429') || error.status === 429) {
        console.log('🔄 Quota do Gemini excedida, usando resposta de fallback...');
      }

      return this.generateFallbackResponse(userMessage, startTime);
    }
  }

  private buildContext(relevantChunks: DocumentChunk[]): string {
    return relevantChunks
      .map(chunk => `[Documento: ${chunk.metadata?.document_name || 'Sem nome'}]\n${chunk.content}`)
      .join('\n\n---\n\n');
  }

  private buildSources(relevantChunks: DocumentChunk[]): Source[] {
    return relevantChunks.map((chunk, index) => ({
      id: chunk.id,
      title: chunk.metadata?.document_name || `Documento ${index + 1}`,
      content: this.truncateContent(chunk.content, 200),
      relevance: chunk.metadata?.similarity || 0.8,
      document_id: chunk.document_id
    }));
  }

  private truncateContent(content: string, maxLength: number): string {
    return content.length > maxLength ? `${content.substring(0, maxLength)}...` : content;
  }

  private buildPrompt(question: string, context: string): string {
    return `${SYSTEM_PROMPT}

CONTEXTO DISPONÍVEL:
${context || 'Nenhum documento específico encontrado.'}

PERGUNTA DO CIDADÃO:
${question}

INSTRUÇÕES PARA RESPOSTA:
1. 🎯 SEJA NATURAL: Responda como a própria Vereadora Rafaela, não como assistente
2. 🚫 NÃO SE APRESENTE: Nunca diga "sou assistente" ou "sou bot"
3. 💬 SEJA CONCISA: Vá direto ao ponto, evite ser prolixo
4. 👋 CUMPRIMENTOS: Responda cordialmente e pergunte como pode ajudar
5. 📋 CONTEXTO: Use o contexto fornecido para responder
6. 🏛️ FOCO: Questões municipais de Parnamirim/RN
7. ✅ HONESTIDADE: Seja transparente se não souber
8. 🔗 ENCAMINHAMENTOS: Ofereça soluções práticas
9. ❤️ EMPATIA: Seja compreensiva mas objetiva

RESPOSTA:`;
  }

  private buildSimplePrompt(question: string): string {
    return `${SYSTEM_PROMPT}

PERGUNTA: ${question}

Responda como assistente da Vereadora Rafaela de Nilda, mesmo sem documentos específicos.`;
  }

  private calculateConfidence(chunksFound: number, answer: string): number {
    let confidence = 0.5;

    // Fatores que aumentam confiança
    if (chunksFound > 0) confidence += 0.2;
    if (chunksFound > 2) confidence += 0.1;
    if (chunksFound > 4) confidence += 0.1;
    if (answer.length > 100) confidence += 0.1;
    if (answer.length > 300) confidence += 0.1;

    // Fatores que diminuem confiança
    const uncertaintyWords = ['não sei', 'não tenho', 'desculpe', 'talvez', 'possivelmente'];
    const hasUncertainty = uncertaintyWords.some(word => 
      answer.toLowerCase().includes(word)
    );
    
    if (hasUncertainty) confidence -= 0.2;

    return Math.max(0, Math.min(1, confidence));
  }

  private async cacheResponse(userMessage: string, response: RAGResponse): Promise<void> {
    try {
      const contextHash = await this.generateContextHash(userMessage);
      await cacheService.setResponse(userMessage, contextHash, response);
    } catch (error) {
      console.warn('⚠️ Erro ao cachear resposta:', error);
    }
  }

  private handleProcessingError(error: unknown, startTime: number): RAGResponse {
    console.error('❌ Erro ao processar mensagem:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    
    return {
      answer: 'Desculpe, ocorreu um erro ao processar sua pergunta. Tente novamente ou reformule sua pergunta.',
      sources: [],
      confidence: 0,
      processing_time: Date.now() - startTime
    };
  }

  // === MÉTODOS DE UPLOAD ===

  private async uploadViaBackend(file: File): Promise<Document> {
    const formData = new FormData();
    formData.append('document', file);

    const response = await this.retryOperation(async () => {
      const res = await fetch(`${this.backendUrl}/api/documents/upload`, {
        method: 'POST',
        body: formData
      });

      if (!res.ok) {
        throw new Error(`Backend upload failed: ${res.status} ${res.statusText}`);
      }

      return res;
    });

    const result = await response.json();
    console.log('✅ Upload via backend bem-sucedido:', result.document.name);
    return result.document;
  }

  private async uploadLocally(file: File): Promise<Document> {
    const document = await databaseService.saveDocument({
      name: file.name,
      type: file.type,
      size: file.size,
      upload_date: new Date(),
      status: 'processing'
    });

    console.log('💾 Documento salvo localmente:', document.id);

    // Processar documento em background
    this.processDocumentAsync(document, file);

    return document;
  }

  private async processDocumentAsync(document: Document, file: File): Promise<void> {
    try {
      console.log('🔄 Processando documento:', document.name);
      
      await databaseService.updateDocumentStatus(document.id, 'processing');

      // Extrair texto
      const text = await this.extractTextFromFile(file);
      
      // Processar chunks
      const processingResult = await this.processDocumentChunks(document, text);
      
      if (processingResult.success) {
        await databaseService.updateDocumentStatus(
          document.id, 
          'ready', 
          processingResult.chunksProcessed
        );
        console.log('✅ Documento processado com sucesso');
      } else {
        throw new Error(processingResult.error || 'Falha no processamento');
      }

    } catch (error) {
      console.error('❌ Erro no processamento do documento:', error);
      await databaseService.updateDocumentStatus(document.id, 'error');
      throw error;
    }
  }

  private async extractTextFromFile(file: File): Promise<string> {
    try {
      if (file.type === 'application/pdf') {
        return await documentProcessor.extractTextFromPDF(file);
      } else if (file.type.startsWith('text/')) {
        return await file.text();
      } else {
        console.warn('⚠️ Tipo de arquivo não suportado, usando texto simulado');
        return this.generateFallbackText(file.name);
      }
    } catch (error) {
      console.warn('⚠️ Erro na extração de texto, usando fallback:', error);
      return this.generateFallbackText(file.name);
    }
  }

  private async processDocumentChunks(document: Document, text: string): Promise<ProcessingResult> {
    const chunks = documentProcessor.chunkText(text, {
      chunkSize: CONFIG.CHUNK_SIZE,
      overlap: CONFIG.CHUNK_OVERLAP
    });

    console.log(`📦 Criados ${chunks.length} chunks`);

    const chunksWithEmbeddings: ChunkWithEmbedding[] = [];
    const errors: string[] = [];

    for (let i = 0; i < chunks.length; i++) {
      try {
        const chunk = chunks[i];
        console.log(`🧮 Gerando embedding ${i + 1}/${chunks.length}`);

        const embedding = await this.retryOperation(async () => {
          return await embeddingService.generateEmbedding(chunk);
        });

        chunksWithEmbeddings.push({
          document_id: document.id,
          content: chunk,
          chunk_index: i,
          embedding: embedding.embedding,
          metadata: {
            page_number: Math.floor(i / 3) + 1,
            section: `Seção ${i + 1}`,
            file_type: document.type,
            created_at: new Date(),
            document_name: document.name
          }
        });

        // Pausa entre requests
        await this.delay(CONFIG.PROCESSING_DELAY);

      } catch (error) {
        const errorMsg = `Erro ao processar chunk ${i + 1}: ${error}`;
        console.warn('⚠️', errorMsg);
        errors.push(errorMsg);
      }
    }

    if (chunksWithEmbeddings.length === 0) {
      return {
        success: false,
        chunksProcessed: 0,
        error: `Nenhum chunk foi processado com sucesso. Erros: ${errors.join('; ')}`
      };
    }

    // Salvar chunks no banco
    await databaseService.saveDocumentChunks(chunksWithEmbeddings);
    console.log(`💾 ${chunksWithEmbeddings.length} chunks salvos no banco`);

    return {
      success: true,
      chunksProcessed: chunksWithEmbeddings.length
    };
  }

  // === MÉTODOS UTILITÁRIOS ===

  private async retryOperation<T>(
    operation: () => Promise<T>,
    maxAttempts: number = CONFIG.RETRY_ATTEMPTS
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        if (attempt === maxAttempts) {
          throw lastError;
        }

        console.warn(`⚠️ Tentativa ${attempt} falhou, tentando novamente...`, lastError.message);
        await this.delay(CONFIG.RETRY_DELAY * attempt);
      }
    }

    throw lastError!;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private async generateContextHash(userMessage: string): Promise<string> {
    try {
      const availableChunks = await databaseService.getDocumentChunks();
      const contextInfo = {
        totalChunks: availableChunks.length,
        lastUpdate: Math.max(...availableChunks.map((c: any) =>
          new Date(c.metadata?.created_at || Date.now()).getTime()
        ), 0),
        documentIds: [...new Set(availableChunks.map((c: any) => c.document_id))].sort(),
        queryLength: userMessage.length
      };

      return this.hashString(JSON.stringify(contextInfo));
    } catch (error) {
      console.warn('⚠️ Erro ao gerar hash do contexto:', error);
      return this.hashString(userMessage + Date.now().toString());
    }
  }

  private hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(36);
  }

  private generateFallbackText(fileName: string): string {
    return `# Documento: ${fileName}

Este é um documento de exemplo que foi carregado no sistema da Vereadora Rafaela de Nilda.

## Informações Gerais sobre Parnamirim/RN

Parnamirim é um município do Rio Grande do Norte, localizado na região metropolitana de Natal.
A cidade possui aproximadamente 267.000 habitantes e é conhecida por suas praias e pelo
desenvolvimento urbano planejado.

## Serviços Municipais

### Saúde
- Unidades Básicas de Saúde (UBS) distribuídas pelos bairros
- Hospital Municipal Dr. Deoclécio Marques de Lucena
- Unidade de Pronto Atendimento (UPA)
- Centro de Especialidades Odontológicas (CEO)

### Educação
- Escolas municipais de ensino fundamental
- Creches municipais
- Programas de educação de jovens e adultos
- Transporte escolar

### Infraestrutura
- Pavimentação de vias públicas
- Sistema de iluminação pública
- Rede de saneamento básico
- Coleta de lixo e limpeza urbana

## Atuação da Vereadora Rafaela de Nilda

A Vereadora Rafaela de Nilda atua em diversas frentes para melhorar a qualidade de vida
dos cidadãos de Parnamirim, incluindo:

- Fiscalização dos serviços públicos
- Proposição de projetos de lei
- Atendimento direto à população
- Articulação com outros poderes
- Transparência na gestão pública

Para mais informações ou para solicitar atendimento, entre em contato através dos
canais oficiais da vereadora.`.trim();
  }

  // === FALLBACK RESPONSES ===

  private async generateFallbackResponse(userMessage: string, startTime: number): Promise<RAGResponse> {
    const responseTime = Date.now() - startTime;

    console.log('🔍 Fallback: Tentando busca semântica sem Gemini...');

    // Tentar busca semântica mesmo sem Gemini
    try {
      const relevantChunks = await semanticSearchService.searchSimilarChunks(userMessage, {
        limit: 3,
        minSimilarity: 0.1,
        boostRecent: true
      });

      if (relevantChunks.length > 0) {
        return this.buildFallbackResponseWithContext(relevantChunks, responseTime);
      }
    } catch (error) {
      console.error('❌ Erro na busca semântica fallback:', error);
    }

    // Resposta baseada em palavras-chave
    return this.buildKeywordBasedResponse(userMessage, responseTime);
  }

  private buildFallbackResponseWithContext(relevantChunks: DocumentChunk[], responseTime: number): RAGResponse {
    const sources = relevantChunks.map(chunk => ({
      id: chunk.id,
      title: chunk.metadata?.document_name || 'Documento',
      content: this.truncateContent(chunk.content, 200),
      relevance: chunk.metadata?.similarity || 0.7,
      document_id: chunk.document_id
    }));

    const contextText = relevantChunks.map(chunk => chunk.content).join('\n\n');
    const answer = `Com base nos documentos disponíveis, encontrei informações relevantes sobre sua pergunta.

${this.truncateContent(contextText, 500)}

Como posso ajudá-lo mais especificamente? 🏛️`;

    return {
      answer,
      sources,
      confidence: 0.7,
      processing_time: responseTime
    };
  }

  private buildKeywordBasedResponse(userMessage: string, responseTime: number): RAGResponse {
    const responses = this.getFallbackResponses();
    const messageKey = userMessage.toLowerCase();

    let answer = responses.default;

    // Buscar resposta baseada em palavras-chave
    for (const [keywords, response] of Object.entries(responses.keywords)) {
      if (keywords.split(',').some(keyword => messageKey.includes(keyword.trim()))) {
        answer = response;
        break;
      }
    }

    return {
      answer,
      sources: [],
      confidence: 0.4,
      processing_time: responseTime
    };
  }

  private getFallbackResponses() {
    const { info, utils } = vereadoraPersona;

    return {
      default: `${utils.getSaudacao()}

🏛️ **Sobre o Gabinete:**
Estou aqui para ajudar com informações sobre o mandato da ${info.nomeCompleto} na Câmara Municipal de ${info.municipio}/${info.estado}.

⚠️ **Modo Demonstração:**
No momento, estou funcionando em modo demonstração. Para acesso completo às funcionalidades e documentos oficiais, é necessário configurar as integrações com os serviços externos.

📞 **Contato Direto:**
Para questões específicas ou urgentes, entre em contato diretamente com o gabinete:
- 📧 Email: ${info.contatos.email}
- 📱 WhatsApp: ${info.contatos.telefone}

Como posso ajudá-lo hoje?`,

      keywords: {
        'projeto,lei,legislação': `📋 **Projetos de Lei**

A Vereadora Rafaela de Nilda trabalha constantemente na elaboração e análise de projetos de lei que beneficiem os cidadãos de Parnamirim.

🔍 **Para consultar projetos específicos:**
- Acesse o site da Câmara Municipal de Parnamirim
- Entre em contato com o gabinete para informações detalhadas

📞 **Contato:** ${info.contatos.telefone}`,

        'horário,atendimento,funcionamento': `🕐 **Horário de Atendimento**

**Gabinete da Vereadora Rafaela de Nilda:**
- Segunda a Sexta: 8h às 17h
- Local: Câmara Municipal de Parnamirim/RN

📞 **Agendamentos:**
- Telefone: ${info.contatos.telefone}
- Email: ${info.contatos.email}

💡 **Dica:** Recomendamos agendar previamente para garantir o atendimento personalizado.`,

        'serviço,público,parnamirim': `🏛️ **Serviços Públicos em Parnamirim**

A Vereadora Rafaela de Nilda atua na fiscalização e melhoria dos serviços públicos municipais:

🚌 **Transporte Público**
🏥 **Saúde Municipal**
📚 **Educação**
🛣️ **Infraestrutura**
🌳 **Meio Ambiente**

📞 **Para denúncias ou sugestões:**
Entre em contato com o gabinete: ${info.contatos.telefone}`,

        'transparência,prestação,contas': `📊 **Transparência e Prestação de Contas**

A Vereadora Rafaela de Nilda preza pela transparência total em seu mandato:

🔍 **Portal da Transparência:**
- Gastos do gabinete
- Projetos apresentados
- Votações e posicionamentos

📱 **Redes Sociais:**
Acompanhe as atividades diárias e relatórios de trabalho.

📞 **Contato:** ${info.contatos.telefone} para esclarecimentos adicionais.`
      }
    };
  }
}

export const vereadoraRAGService = new VereadoraRAGService();