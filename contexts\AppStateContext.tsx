import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { whatsappService } from '../services/whatsappService';

// Tipos
interface WhatsAppState {
  isConnected: boolean;
  status: string;
  qrCode: string | null;
  loading: boolean;
  error: string | null;
  lastUpdate: Date | null;
}

interface DocumentState {
  documents: any[];
  loading: boolean;
  error: string | null;
  lastUpdate: Date | null;
}

interface AppState {
  whatsapp: WhatsAppState;
  documents: DocumentState;
  isInitialized: boolean;
}

// Estado inicial
const initialState: AppState = {
  whatsapp: {
    isConnected: false,
    status: 'Verificando...',
    qrCode: null,
    loading: true,
    error: null,
    lastUpdate: null
  },
  documents: {
    documents: [],
    loading: false,
    error: null,
    lastUpdate: null
  },
  isInitialized: false
};

// Actions
type AppAction = 
  | { type: 'WHATSAPP_LOADING'; payload: boolean }
  | { type: 'WHATSAPP_UPDATE'; payload: Partial<WhatsAppState> }
  | { type: 'WHATSAPP_ERROR'; payload: string }
  | { type: 'DOCUMENTS_LOADING'; payload: boolean }
  | { type: 'DOCUMENTS_UPDATE'; payload: any[] }
  | { type: 'DOCUMENTS_ERROR'; payload: string }
  | { type: 'SET_INITIALIZED'; payload: boolean };

// Reducer
function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'WHATSAPP_LOADING':
      return {
        ...state,
        whatsapp: {
          ...state.whatsapp,
          loading: action.payload,
          error: action.payload ? null : state.whatsapp.error
        }
      };

    case 'WHATSAPP_UPDATE':
      return {
        ...state,
        whatsapp: {
          ...state.whatsapp,
          ...action.payload,
          loading: false,
          error: null,
          lastUpdate: new Date()
        }
      };

    case 'WHATSAPP_ERROR':
      return {
        ...state,
        whatsapp: {
          ...state.whatsapp,
          loading: false,
          error: action.payload,
          lastUpdate: new Date()
        }
      };

    case 'DOCUMENTS_LOADING':
      return {
        ...state,
        documents: {
          ...state.documents,
          loading: action.payload,
          error: action.payload ? null : state.documents.error
        }
      };

    case 'DOCUMENTS_UPDATE':
      return {
        ...state,
        documents: {
          ...state.documents,
          documents: action.payload,
          loading: false,
          error: null,
          lastUpdate: new Date()
        }
      };

    case 'DOCUMENTS_ERROR':
      return {
        ...state,
        documents: {
          ...state.documents,
          loading: false,
          error: action.payload,
          lastUpdate: new Date()
        }
      };

    case 'SET_INITIALIZED':
      return {
        ...state,
        isInitialized: action.payload
      };

    default:
      return state;
  }
}

// Context
const AppStateContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  actions: {
    updateWhatsAppStatus: () => Promise<void>;
    updateDocuments: () => Promise<void>;
    refreshAll: () => Promise<void>;
  };
} | null>(null);

// Provider
export const AppStateProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Ações
  const updateWhatsAppStatus = async () => {
    try {
      console.log('🔄 AppState: Atualizando status WhatsApp...');
      dispatch({ type: 'WHATSAPP_LOADING', payload: true });

      const status = await whatsappService.getStatus();
      console.log('📊 AppState: Status recebido:', status);

      if (status) {
        dispatch({ 
          type: 'WHATSAPP_UPDATE', 
          payload: {
            isConnected: status.isConnected || false,
            status: status.status || 'Desconectado',
            qrCode: status.qrCode?.base64 || null
          }
        });
      } else {
        dispatch({ 
          type: 'WHATSAPP_ERROR', 
          payload: 'Não foi possível obter status do WhatsApp'
        });
      }
    } catch (error) {
      console.error('❌ AppState: Erro ao atualizar WhatsApp:', error);
      dispatch({ 
        type: 'WHATSAPP_ERROR', 
        payload: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  };

  const updateDocuments = async () => {
    try {
      console.log('🔄 AppState: Atualizando documentos...');
      dispatch({ type: 'DOCUMENTS_LOADING', payload: true });

      const response = await fetch('http://localhost:3002/api/documents');
      if (!response.ok) throw new Error('Erro ao carregar documentos');

      const data = await response.json();
      console.log('📊 AppState: Documentos recebidos:', data);

      dispatch({ 
        type: 'DOCUMENTS_UPDATE', 
        payload: data.documents || []
      });
    } catch (error) {
      console.error('❌ AppState: Erro ao atualizar documentos:', error);
      dispatch({ 
        type: 'DOCUMENTS_ERROR', 
        payload: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  };

  const refreshAll = async () => {
    console.log('🔄 AppState: Atualizando tudo...');
    await Promise.all([
      updateWhatsAppStatus(),
      updateDocuments()
    ]);
  };

  // Inicialização
  useEffect(() => {
    const initialize = async () => {
      console.log('🚀 AppState: Inicializando...');
      await refreshAll();
      dispatch({ type: 'SET_INITIALIZED', payload: true });
    };

    initialize();

    // Polling para atualizações automáticas
    const interval = setInterval(async () => {
      if (state.isInitialized) {
        await updateWhatsAppStatus();
      }
    }, 10000); // 10 segundos

    return () => clearInterval(interval);
  }, []);

  const actions = {
    updateWhatsAppStatus,
    updateDocuments,
    refreshAll
  };

  return (
    <AppStateContext.Provider value={{ state, dispatch, actions }}>
      {children}
    </AppStateContext.Provider>
  );
};

// Hook
export const useAppState = () => {
  const context = useContext(AppStateContext);
  if (!context) {
    throw new Error('useAppState deve ser usado dentro de AppStateProvider');
  }
  return context;
};
