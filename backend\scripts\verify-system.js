#!/usr/bin/env node

import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import path from 'path';

const execAsync = promisify(exec);

class SystemVerifier {
  constructor() {
    this.baseUrl = 'http://localhost:3001';
    this.frontendUrl = 'http://localhost:3000';
    this.results = {
      backend: {},
      frontend: {},
      services: {},
      files: {},
      overall: 'unknown'
    };
  }

  async verifyAll() {
    console.log('🔍 Verificando Sistema Completo...\n');
    
    await this.verifyFiles();
    await this.verifyBackend();
    await this.verifyFrontend();
    await this.verifyServices();
    
    this.generateReport();
  }

  async verifyFiles() {
    console.log('📁 Verificando arquivos e diretórios...');
    
    const requiredFiles = [
      'package.json',
      'server-centralized.js',
      '.env.example',
      'data',
      'data/sessions',
      'data/backups',
      'data/logs'
    ];

    for (const file of requiredFiles) {
      const exists = fs.existsSync(file);
      this.results.files[file] = exists ? '✅' : '❌';
      console.log(`  ${exists ? '✅' : '❌'} ${file}`);
    }
    
    console.log('');
  }

  async verifyBackend() {
    console.log('🔧 Verificando Backend...');
    
    try {
      // Verificar se o servidor está rodando
      const { default: fetch } = await import('node-fetch');
      
      // Health check básico
      try {
        const response = await fetch(`${this.baseUrl}/api/health`, { timeout: 5000 });
        const data = await response.json();
        this.results.backend.health = response.ok ? '✅' : '❌';
        this.results.backend.uptime = data.uptime || 0;
        console.log(`  ✅ Health Check: ${data.status}`);
      } catch (error) {
        this.results.backend.health = '❌';
        console.log(`  ❌ Health Check: Servidor não responde`);
      }

      // Health check detalhado
      try {
        const response = await fetch(`${this.baseUrl}/api/health/detailed`, { timeout: 10000 });
        const data = await response.json();
        this.results.backend.detailed = response.ok ? '✅' : '❌';
        
        console.log(`  📊 Status Geral: ${data.status}`);
        console.log(`  🔧 RAG Service: ${data.services?.rag?.status || 'unknown'}`);
        console.log(`  📱 WhatsApp Service: ${data.services?.whatsapp?.status || 'unknown'}`);
        console.log(`  💾 Session Manager: ${data.services?.sessionManager?.status || 'unknown'}`);
        console.log(`  🤖 Message Handler: ${data.services?.messageHandler?.status || 'unknown'}`);
      } catch (error) {
        this.results.backend.detailed = '❌';
        console.log(`  ❌ Health Detailed: ${error.message}`);
      }

    } catch (error) {
      this.results.backend.health = '❌';
      console.log(`  ❌ Backend Error: ${error.message}`);
    }
    
    console.log('');
  }

  async verifyFrontend() {
    console.log('🌐 Verificando Frontend...');
    
    try {
      const { default: fetch } = await import('node-fetch');
      const response = await fetch(this.frontendUrl, { timeout: 5000 });
      this.results.frontend.status = response.ok ? '✅' : '❌';
      console.log(`  ${response.ok ? '✅' : '❌'} Frontend acessível: ${response.status}`);
    } catch (error) {
      this.results.frontend.status = '❌';
      console.log(`  ❌ Frontend não acessível: ${error.message}`);
    }
    
    console.log('');
  }

  async verifyServices() {
    console.log('🔌 Verificando Serviços Externos...');
    
    // Verificar Docker
    try {
      await execAsync('docker --version');
      this.results.services.docker = '✅';
      console.log('  ✅ Docker instalado');
      
      // Verificar WPPConnect
      try {
        const { stdout } = await execAsync('docker ps --filter name=wppconnect --format "{{.Names}}"');
        if (stdout.trim()) {
          this.results.services.wppconnect = '✅';
          console.log('  ✅ WPPConnect Server rodando');
        } else {
          this.results.services.wppconnect = '⚠️';
          console.log('  ⚠️ WPPConnect Server não está rodando');
        }
      } catch (error) {
        this.results.services.wppconnect = '❌';
        console.log('  ❌ Erro ao verificar WPPConnect');
      }
    } catch (error) {
      this.results.services.docker = '❌';
      this.results.services.wppconnect = '❌';
      console.log('  ❌ Docker não instalado');
    }

    // Verificar Node.js
    try {
      const { stdout } = await execAsync('node --version');
      this.results.services.node = '✅';
      console.log(`  ✅ Node.js: ${stdout.trim()}`);
    } catch (error) {
      this.results.services.node = '❌';
      console.log('  ❌ Node.js não encontrado');
    }

    // Verificar NPM
    try {
      const { stdout } = await execAsync('npm --version');
      this.results.services.npm = '✅';
      console.log(`  ✅ NPM: ${stdout.trim()}`);
    } catch (error) {
      this.results.services.npm = '❌';
      console.log('  ❌ NPM não encontrado');
    }
    
    console.log('');
  }

  generateReport() {
    console.log('📋 RELATÓRIO FINAL\n');
    console.log('='.repeat(50));
    
    // Calcular status geral
    const allChecks = [
      ...Object.values(this.results.files),
      ...Object.values(this.results.backend),
      ...Object.values(this.results.frontend),
      ...Object.values(this.results.services)
    ];
    
    const successCount = allChecks.filter(check => check === '✅').length;
    const totalChecks = allChecks.length;
    const successRate = (successCount / totalChecks) * 100;
    
    if (successRate >= 90) {
      this.results.overall = '🎉 EXCELENTE';
    } else if (successRate >= 70) {
      this.results.overall = '✅ BOM';
    } else if (successRate >= 50) {
      this.results.overall = '⚠️ PARCIAL';
    } else {
      this.results.overall = '❌ CRÍTICO';
    }
    
    console.log(`Status Geral: ${this.results.overall}`);
    console.log(`Taxa de Sucesso: ${successRate.toFixed(1)}% (${successCount}/${totalChecks})`);
    console.log('');
    
    // Resumo por categoria
    console.log('📊 RESUMO POR CATEGORIA:');
    console.log('');
    
    console.log('📁 Arquivos:');
    Object.entries(this.results.files).forEach(([file, status]) => {
      console.log(`  ${status} ${file}`);
    });
    console.log('');
    
    console.log('🔧 Backend:');
    Object.entries(this.results.backend).forEach(([service, status]) => {
      console.log(`  ${status} ${service}`);
    });
    console.log('');
    
    console.log('🌐 Frontend:');
    Object.entries(this.results.frontend).forEach(([service, status]) => {
      console.log(`  ${status} ${service}`);
    });
    console.log('');
    
    console.log('🔌 Serviços:');
    Object.entries(this.results.services).forEach(([service, status]) => {
      console.log(`  ${status} ${service}`);
    });
    console.log('');
    
    // Recomendações
    this.generateRecommendations();
  }

  generateRecommendations() {
    console.log('💡 RECOMENDAÇÕES:');
    console.log('');
    
    if (this.results.backend.health === '❌') {
      console.log('🔧 Backend não está rodando:');
      console.log('   npm run dev');
      console.log('');
    }
    
    if (this.results.frontend.status === '❌') {
      console.log('🌐 Frontend não está rodando:');
      console.log('   cd .. && npm run dev');
      console.log('');
    }
    
    if (this.results.services.docker === '❌') {
      console.log('🐳 Docker não instalado:');
      console.log('   Consulte: docs/DOCKER_WPPCONNECT_SETUP.md');
      console.log('');
    }
    
    if (this.results.services.wppconnect === '⚠️') {
      console.log('📱 WPPConnect não está rodando:');
      console.log('   npm run start:wppconnect');
      console.log('');
    }
    
    const missingFiles = Object.entries(this.results.files)
      .filter(([_, status]) => status === '❌')
      .map(([file, _]) => file);
    
    if (missingFiles.length > 0) {
      console.log('📁 Arquivos/diretórios faltando:');
      missingFiles.forEach(file => {
        if (file.startsWith('data/')) {
          console.log(`   mkdir -p ${file}`);
        }
      });
      console.log('');
    }
    
    console.log('📚 Para mais informações:');
    console.log('   README.md');
    console.log('   docs/DOCKER_WPPCONNECT_SETUP.md');
    console.log('');
  }
}

// Executar se chamado diretamente
const verifier = new SystemVerifier();
verifier.verifyAll().catch(console.error);

export default SystemVerifier;
