import { GoogleGenerativeAI } from '@google/generative-ai';
import { Logger } from './Logger.js';

export class LLM {
  constructor() {
    this.logger = new Logger();
    this.apiKey = process.env.GEMINI_API_KEY;
    this.model = null;
    this.isInitialized = false;

    // Configurações
    this.maxTokens = parseInt(process.env.MAX_TOKENS) || 1000;
    this.temperature = parseFloat(process.env.TEMPERATURE) || 0.7;
    this.topP = parseFloat(process.env.TOP_P) || 0.9;
    this.topK = parseInt(process.env.TOP_K) || 40;

    // Cache de respostas
    this.responseCache = new Map();
    this.maxCacheSize = 100;

    // Fallback responses
    this.fallbackResponses = this.initializeFallbackResponses();

    this.initialize();
  }

  async initialize() {
    try {
      if (!this.apiKey) {
        this.logger.warn('⚠️ GEMINI_API_KEY não configurada, usando modo fallback');
        this.isInitialized = false;
        return;
      }

      const genAI = new GoogleGenerativeAI(this.apiKey);
      this.model = genAI.getGenerativeModel({
        model: 'gemini-1.5-flash',
        generationConfig: {
          temperature: this.temperature,
          topP: this.topP,
          topK: this.topK,
          maxOutputTokens: this.maxTokens,
        }
      });

      // Testar a conexão
      await this.testConnection();

      this.isInitialized = true;
      this.logger.info('✅ LLM Service inicializado com Gemini');
    } catch (error) {
      this.logger.error('❌ Erro ao inicializar LLM:', error);
      this.isInitialized = false;
    }
  }

  async testConnection() {
    try {
      const result = await this.model.generateContent('Teste de conexão');
      const response = result.response;
      this.logger.debug('✅ Conexão com Gemini testada com sucesso');
      return response.text();
    } catch (error) {
      this.logger.error('❌ Erro no teste de conexão:', error);
      throw error;
    }
  }

  async generateResponse(prompt, context = '', userInfo = {}) {
    try {
      // Verificar cache primeiro
      const cacheKey = this.generateCacheKey(prompt, context);
      if (this.responseCache.has(cacheKey)) {
        this.logger.debug('📋 Resposta encontrada no cache');
        return this.responseCache.get(cacheKey);
      }

      let response;

      if (this.isInitialized && this.model) {
        response = await this.generateWithGemini(prompt, context, userInfo);
      } else {
        response = await this.generateFallbackResponse(prompt, context, userInfo);
      }

      // Adicionar ao cache
      this.addToCache(cacheKey, response);

      return response;
    } catch (error) {
      this.logger.error('❌ Erro ao gerar resposta:', error);
      return await this.generateFallbackResponse(prompt, context, userInfo);
    }
  }

  async generateWithGemini(prompt, context, userInfo) {
    try {
      const fullPrompt = this.buildFullPrompt(prompt, context, userInfo);

      this.logger.debug('🤖 Gerando resposta com Gemini...');
      const result = await this.model.generateContent(fullPrompt);
      const response = result.response;
      const text = response.text();

      // Pós-processar a resposta
      const processedResponse = this.postProcessResponse(text, userInfo);

      this.logger.debug('✅ Resposta gerada com Gemini');
      return processedResponse;
    } catch (error) {
      this.logger.error('❌ Erro ao gerar com Gemini:', error);
      throw error;
    }
  }

  buildFullPrompt(userMessage, context, userInfo) {
    const userName = userInfo.name || 'Cidadão';
    const isBusinessHours = this.isBusinessHours();

    return `Você é a assistente virtual da Vereadora Rafaela de Nilda, da Câmara Municipal de Parnamirim/RN.

CONTEXTO RELEVANTE:
${context || 'Nenhum contexto específico disponível.'}

INFORMAÇÕES DO USUÁRIO:
- Nome: ${userName}
- Horário: ${isBusinessHours ? 'Horário comercial' : 'Fora do horário comercial'}

INSTRUÇÕES:
1. Responda como a assistente da Vereadora Rafaela de Nilda
2. Seja cordial, profissional e prestativa
3. Use informações do contexto quando relevante
4. Mantenha foco em questões municipais de Parnamirim/RN
5. Se não souber algo, seja honesta e ofereça alternativas
6. Use emojis moderadamente para tornar a conversa mais amigável
7. Mantenha respostas concisas mas informativas

PERGUNTA DO USUÁRIO:
${userMessage}

RESPOSTA:`;
  }

  async generateFallbackResponse(prompt, _context, userInfo) {
    this.logger.debug('🔄 Gerando resposta fallback...');

    const userName = userInfo.name || 'Cidadão';
    const messageKey = prompt.toLowerCase();

    // Buscar resposta baseada em palavras-chave
    for (const [keywords, response] of Object.entries(this.fallbackResponses)) {
      if (keywords.split(',').some(keyword => messageKey.includes(keyword.trim()))) {
        return this.personalizeResponse(response, userName);
      }
    }

    // Resposta padrão
    return this.personalizeResponse(this.fallbackResponses.default, userName);
  }

  initializeFallbackResponses() {
    return {
      'default': `Olá! Sou a assistente virtual da Vereadora Rafaela de Nilda.

Como posso ajudá-lo hoje? Posso fornecer informações sobre:
• Projetos de lei e atividades legislativas
• Serviços públicos de Parnamirim
• Horários de atendimento do gabinete
• Contatos e informações institucionais

Para questões específicas ou agendamentos, entre em contato pelo telefone do gabinete.`,

      'projeto,lei,legislação,proposta': `A Vereadora Rafaela de Nilda trabalha ativamente em diversos projetos para melhorar Parnamirim! 💪

Principais áreas de atuação:
• 🏥 Saúde pública e qualidade dos serviços
• 🚌 Transporte público e mobilidade urbana
• 🌳 Meio ambiente e sustentabilidade
• 👥 Assistência social e direitos humanos
• 🏫 Educação e cultura

Para informações específicas sobre projetos em andamento, consulte o site da Câmara Municipal ou entre em contato com o gabinete.`,

      'saúde,hospital,posto,sus': `🏥 **Saúde Pública em Parnamirim**

A Vereadora Rafaela atua na fiscalização e melhoria dos serviços de saúde:

• Acompanhamento das Unidades Básicas de Saúde
• Fiscalização de hospitais e clínicas
• Propostas para melhorar o atendimento
• Defesa do SUS e acesso universal à saúde

Para denúncias ou sugestões sobre saúde pública, entre em contato conosco. Sua voz é importante para melhorarmos os serviços!`,

      'transporte,ônibus,mobilidade,trânsito': `🚌 **Transporte e Mobilidade Urbana**

Acompanhamos de perto as questões de mobilidade em Parnamirim:

• Qualidade e pontualidade do transporte público
• Acessibilidade nos ônibus e paradas
• Propostas para novas linhas e horários
• Infraestrutura viária e sinalização
• Ciclovias e mobilidade sustentável

Tem alguma reclamação ou sugestão sobre transporte? Nos informe! Trabalhamos para melhorar a mobilidade de todos os cidadãos.`,

      'horário,atendimento,contato,telefone': `📞 **Atendimento e Contatos**

**Horário de Funcionamento:**
Segunda a sexta-feira: 8h às 17h

**Contatos:**
• Telefone: ${process.env.GABINETE_TELEFONE || '(84) 99999-9999'}
• Email: <EMAIL>
• Endereço: Câmara Municipal de Parnamirim/RN

**Como agendar atendimento:**
Ligue para o gabinete ou envie mensagem para marcar um horário. Estamos sempre prontos para ouvir e ajudar os cidadãos de Parnamirim! 😊`
    };
  }

  personalizeResponse(response, userName) {
    if (userName && userName !== 'Cidadão' && userName !== 'unknown') {
      return response.replace(/Olá!/g, `Olá, ${userName}!`);
    }
    return response;
  }

  postProcessResponse(text, _userInfo) {
    // Limpar e formatar a resposta
    let processed = text.trim();

    // Remover marcadores de markdown desnecessários
    processed = processed.replace(/\*\*(.*?)\*\*/g, '*$1*');

    // Garantir que não seja muito longa
    if (processed.length > 1500) {
      processed = processed.substring(0, 1500) + '...';
    }

    // Adicionar saudação se necessário
    if (!processed.toLowerCase().includes('olá') && !processed.toLowerCase().includes('oi')) {
      const greeting = this.getGreeting();
      processed = `${greeting}! ${processed}`;
    }

    return processed;
  }

  getGreeting() {
    const hour = new Date().getHours();
    if (hour >= 5 && hour < 12) return 'Bom dia';
    if (hour >= 12 && hour < 18) return 'Boa tarde';
    return 'Boa noite';
  }

  isBusinessHours() {
    const now = new Date();
    const hour = now.getHours();
    const day = now.getDay(); // 0 = domingo, 1 = segunda, etc.

    const startHour = parseInt(process.env.HORARIO_INICIO?.split(':')[0]) || 8;
    const endHour = parseInt(process.env.HORARIO_FIM?.split(':')[0]) || 18;
    const workDays = process.env.DIAS_FUNCIONAMENTO?.split(',').map(d => parseInt(d)) || [1,2,3,4,5];

    return workDays.includes(day) && hour >= startHour && hour < endHour;
  }

  generateCacheKey(prompt, context) {
    const combined = `${prompt}|${context}`;
    let hash = 0;
    for (let i = 0; i < combined.length; i++) {
      const char = combined.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return `cache_${Math.abs(hash)}`;
  }

  addToCache(key, response) {
    // Limitar tamanho do cache
    if (this.responseCache.size >= this.maxCacheSize) {
      const firstKey = this.responseCache.keys().next().value;
      this.responseCache.delete(firstKey);
    }

    this.responseCache.set(key, response);
  }

  clearCache() {
    this.responseCache.clear();
    this.logger.info('🧹 Cache de respostas limpo');
  }

  getStats() {
    return {
      isInitialized: this.isInitialized,
      hasApiKey: !!this.apiKey,
      cacheSize: this.responseCache.size,
      maxCacheSize: this.maxCacheSize,
      maxTokens: this.maxTokens,
      temperature: this.temperature,
      topP: this.topP,
      topK: this.topK
    };
  }

  async shutdown() {
    this.clearCache();
    this.model = null;
    this.isInitialized = false;
    this.logger.info('🔄 LLM Service finalizado');
  }
}