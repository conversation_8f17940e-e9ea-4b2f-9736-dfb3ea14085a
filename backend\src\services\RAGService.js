import axios from 'axios';
import { Logger } from '../utils/Logger.js';
import VectorStore from '../utils/vectorStore.js';
import { LLM } from '../utils/languageModel.js';
import { DocumentService } from './DocumentService.js';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { createClient } from '@supabase/supabase-js';
// import pdfParse from 'pdf-parse'; // Será importado dinamicamente
import fs from 'fs';
import path from 'path';
import { supabaseConfig } from '../config/supabase.js';

export class RAGService {
  constructor() {
    this.logger = new Logger();
    this.frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
    this.ragApiUrl = process.env.RAG_API_URL || 'http://localhost:3000/api';
    this.isInitialized = false;

    // <PERSON>ão inicializar VectorStore no construtor
    this.vectorStore = null;
    this.llm = null;
    this.documents = new Map(); // Cache de documentos
    this.supabase = supabaseConfig;
    
    // Configuração do axios
    this.httpClient = axios.create({
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Vereadora-Rafaela-WhatsApp-Backend/1.0.0'
      }
    });

    // Configurar interceptors
    this.setupInterceptors();

    this.documentService = new DocumentService();
    // this.supabase já foi definido na linha 24 como supabaseConfig
    this.genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    this.model = this.genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
  }

  setupInterceptors() {
    // Request interceptor
    this.httpClient.interceptors.request.use(
      (config) => {
        this.logger.debug(`🔄 Fazendo requisição para: ${config.url}`);
        return config;
      },
      (error) => {
        this.logger.error('❌ Erro na requisição:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.httpClient.interceptors.response.use(
      (response) => {
        this.logger.debug(`✅ Resposta recebida de: ${response.config.url}`);
        return response;
      },
      (error) => {
        this.logger.error('❌ Erro na resposta:', error.message);
        return Promise.reject(error);
      }
    );
  }

  async initialize() {
    this.logger.info('🔄 Inicializando RAG Service...');

    try {
      // Inicializar Supabase
      await this.supabase.initialize();

      // Inicializar VectorStore apenas quando necessário
      if (!this.vectorStore) {
        this.logger.info('🔧 Inicializando VectorStore...');
        this.vectorStore = new VectorStore();
        // Inicializar explicitamente o VectorStore
        await this.vectorStore.initialize();
      }

      // Inicializar LLM
      if (!this.llm) {
        this.logger.info('🔧 Inicializando LLM...');
        this.llm = new LLM();
      }

      // Testar conexão com o frontend (opcional)
      try {
        await this.testConnection();
        this.logger.info('✅ Conexão com frontend estabelecida');
      } catch (error) {
        this.logger.warn('⚠️ Frontend não disponível, continuando em modo local');
      }

      this.isInitialized = true;
      this.logger.info('✅ RAG Service inicializado com sucesso!');
      return true;
    } catch (error) {
      this.logger.error('❌ Erro ao inicializar RAG Service:', error);
      // Não falhar completamente - pode funcionar em modo offline
      this.isInitialized = false;
      return false;
    }
  }

  async testConnection() {
    try {
      const response = await this.httpClient.get(`${this.frontendUrl}/`);
      this.logger.info('✅ Conexão com frontend estabelecida');
      return response.data;
    } catch (error) {
      this.logger.warn('⚠️ Não foi possível conectar com o frontend');
      throw error;
    }
  }

 async processMessage(userMessage, userInfo = {}) {
    try {
      // Verificar se o VectorStore está disponível
      if (!this.vectorStore) {
        this.logger.warn('⚠️ VectorStore não inicializado, usando resposta simulada');
        return this.generateSimulatedResponse(userMessage);
      }

      // Recuperar documentos relevantes
      const relevantDocs = await this.vectorStore.similaritySearch(userMessage, 3);

      // Construir o contexto
      const context = relevantDocs.map(doc => doc.pageContent).join('\n\n');

      // Gerar resposta usando o modelo de linguagem
      const prompt = this.buildPrompt(userMessage, context, userInfo);
      const response = await this.llm.generateResponse(prompt);

      // Calcular confiança
      const confidence = this.calculateConfidence(relevantDocs, response);

      return {
        answer: response,
        confidence,
        sources: relevantDocs.map(doc => doc.metadata),
      };
    } catch (error) {
      this.logger.error('Erro ao processar mensagem RAG:', error);
      return this.getFallbackResponse(userMessage, userInfo);
    }
  }

  formatWhatsAppResponse(ragResponse, userInfo = {}) {
    const userName = userInfo.name || 'Cidadão';
    const isBusinessHours = this.isBusinessHours();
    
    let formattedMessage = '';

    // Saudação personalizada
    if (this.shouldIncludeGreeting(userInfo)) {
      const greeting = this.getGreeting(userName);
      formattedMessage += `${greeting}! 👋\n\n`;
    }

    // Resposta principal
    formattedMessage += ragResponse.answer;

    // Adicionar fontes se disponíveis
    if (ragResponse.sources && ragResponse.sources.length > 0) {
      formattedMessage += '\n\n📚 *Fontes consultadas:*\n';
      ragResponse.sources.slice(0, 2).forEach((source, index) => {
        formattedMessage += `${index + 1}. ${source.title}\n`;
      });
    }

    // Rodapé institucional
    formattedMessage += this.getFooter(isBusinessHours);

    return {
      message: formattedMessage,
      confidence: ragResponse.confidence,
      sources: ragResponse.sources,
      isFromRAG: true,
      timestamp: new Date().toISOString()
    };
  }

  getFallbackResponse(userMessage, userInfo = {}) {
    const userName = userInfo.name || 'Cidadão';
    const isBusinessHours = this.isBusinessHours();
    
    // Respostas baseadas em palavras-chave
    const responses = this.getFallbackResponses(userName);
    const messageKey = userMessage.toLowerCase();

    let answer = responses.default;
    
    // Buscar resposta baseada em palavras-chave
    for (const [keywords, response] of Object.entries(responses.keywords)) {
      if (keywords.split(',').some(keyword => messageKey.includes(keyword.trim()))) {
        answer = response;
        break;
      }
    }

    const greeting = this.getGreeting(userName);
    const formattedMessage = `${greeting}! 👋\n\n${answer}${this.getFooter(isBusinessHours)}`;

    return {
      message: formattedMessage,
      confidence: 0.6,
      sources: [],
      isFromRAG: false,
      timestamp: new Date().toISOString()
    };
  }

  getFallbackResponses(userName = null) {
    const greeting = userName && userName !== 'Cidadão' && userName !== 'unknown'
      ? `Oi, ${userName}! Como posso te ajudar? 😊`
      : `Oi! Como posso te ajudar? 😊`;

    return {
      default: greeting,

      keywords: {
        'projeto,lei,legislação': `Trabalho em vários projetos para melhorar nossa cidade! 💪 Saúde, transporte, meio ambiente... Em que posso te ajudar especificamente?`,

        'horário,atendimento,funcionamento': `Estou no gabinete de segunda a sexta, das 8h às 17h! 😊 Quer marcar um horário? Me chama no ${process.env.GABINETE_TELEFONE || '(84) 99999-9999'}`,

        'saúde,hospital,posto': `🏥 *Saúde Pública em Parnamirim*

A Vereadora Rafaela atua na fiscalização e melhoria dos serviços de saúde:

• Acompanhamento das UBS
• Fiscalização de hospitais
• Propostas de melhorias
• Defesa do SUS

Para denúncias ou sugestões sobre saúde pública, entre em contato conosco.`,

        'transporte,ônibus,mobilidade': `🚌 *Transporte Público*

Acompanhamos de perto as questões de mobilidade urbana:

• Qualidade do transporte público
• Acessibilidade nos ônibus
• Novas linhas e horários
• Infraestrutura de paradas

Tem alguma reclamação ou sugestão sobre transporte? Nos informe!`
      }
    };
  }

  getGreeting(userName = null) {
    const hour = new Date().getHours();
    let greeting;

    if (hour >= 5 && hour < 12) {
      greeting = 'Bom dia';
    } else if (hour >= 12 && hour < 18) {
      greeting = 'Boa tarde';
    } else {
      greeting = 'Boa noite';
    }

    // Personalizar com nome se disponível
    if (userName && userName !== 'Cidadão' && userName !== 'unknown') {
      return `${greeting}, ${userName}`;
    }

    return greeting;
  }

  shouldIncludeGreeting(userInfo) {
    // Incluir saudação se for a primeira mensagem da conversa
    return !userInfo.hasGreeted;
  }

  isBusinessHours() {
    const now = new Date();
    const hour = now.getHours();
    const day = now.getDay(); // 0 = domingo, 1 = segunda, etc.
    
    const startHour = parseInt(process.env.HORARIO_INICIO?.split(':')[0]) || 8;
    const endHour = parseInt(process.env.HORARIO_FIM?.split(':')[0]) || 18;
    const workDays = process.env.DIAS_FUNCIONAMENTO?.split(',').map(d => parseInt(d)) || [1,2,3,4,5];
    
    return workDays.includes(day) && hour >= startHour && hour < endHour;
  }

  getFooter(isBusinessHours) {
    const footer = `\n\n---\n🏛️ *Vereadora Rafaela de Nilda*\n📍 Câmara Municipal de Parnamirim/RN`;
    
    if (!isBusinessHours) {
      return footer + `\n\n⏰ *Fora do horário comercial*\nResponderemos em breve no próximo dia útil.`;
    }
    
    return footer + `\n\n📞 ${process.env.GABINETE_TELEFONE || '(84) 99999-9999'}`;
  }

  async uploadDocument(documentData) {
    try {
      this.logger.info('📄 Enviando documento para processamento RAG...');
      
      const response = await this.httpClient.post(`${this.ragApiUrl}/documents`, documentData);
      
      this.logger.info('✅ Documento enviado com sucesso');
      return response.data;
      
    } catch (error) {
      this.logger.error('❌ Erro ao enviar documento:', error);
      throw error;
    }
  }

  async getDocuments() {
    try {
      const response = await this.httpClient.get(`${this.ragApiUrl}/documents`);
      return response.data;
    } catch (error) {
      this.logger.error('❌ Erro ao obter documentos:', error);
      throw error;
    }
  }

  async getConversations() {
    try {
      const response = await this.httpClient.get(`${this.ragApiUrl}/conversations`);
      return response.data;
    } catch (error) {
      this.logger.error('❌ Erro ao obter conversas:', error);
      throw error;
    }
  }

  getStatus() {
    return {
      isInitialized: this.isInitialized,
      frontendUrl: this.frontendUrl,
      ragApiUrl: this.ragApiUrl,
      timestamp: new Date().toISOString()
    };
  }

  buildPrompt(userMessage, context, userInfo) {
    // Implementar lógica para construir o prompt
    return `Mensagem do usuário: ${userMessage}\nContexto: ${context}\nInformações do usuário: ${JSON.stringify(userInfo)}`;
  }

  calculateConfidence(relevantDocs, response) {
    // Implementar lógica para calcular a confiança
    return 0.8; // Valor padrão
  }

  /**
   * Processa uma pergunta usando RAG
   */
  async processQuery(query, conversationId = null, options = {}) {
    const startTime = Date.now();

    try {
      console.log(`🔍 Processando query RAG: "${query}"`);

      // 1. Detectar se a pergunta é sobre um documento específico
      const specificDocument = this.detectSpecificDocument(query);

      // 2. Buscar contexto relevante (com filtro por documento se necessário)
      const searchOptions = {
        limit: options.maxSources || 5,
        threshold: options.threshold || 0.7
      };

      // Se detectou documento específico, filtrar apenas por ele
      if (specificDocument) {
        console.log(`🎯 Pergunta específica sobre documento: ${specificDocument}`);
        searchOptions.documentFilter = specificDocument;
      }

      const searchResults = await this.documentService.semanticSearch(query, searchOptions);

      // 3. Obter contexto da conversa se disponível
      const conversationContext = conversationId
        ? await this.getConversationContext(conversationId)
        : null;

      // 3. Construir prompt com contexto
      const prompt = this.buildRAGPrompt(query, searchResults, conversationContext);

      // 4. Gerar resposta com Gemini
      const response = await this.generateResponse(prompt);

      // 5. Salvar na conversa se aplicável
      if (conversationId) {
        await this.saveConversationMessage(conversationId, 'user', query);
        await this.saveConversationMessage(conversationId, 'assistant', response, searchResults);
      }

      const processingTime = Date.now() - startTime;

      return {
        answer: response,
        sources: searchResults,
        confidence_score: this.calculateConfidenceScore(searchResults),
        processing_time: processingTime,
        conversation_id: conversationId
      };

    } catch (error) {
      console.error('❌ Erro no processamento RAG:', error);
      throw error;
    }
  }

  /**
   * Constrói o prompt para o modelo com contexto RAG
   */
  buildRAGPrompt(query, searchResults, conversationContext = null) {
    let prompt = `Você é um assistente inteligente da Vereadora Rafaela de Nilda de Parnamirim/RN. 
Sua função é responder perguntas dos cidadãos com base nos documentos oficiais e informações do mandato.

INSTRUÇÕES:
- Use APENAS as informações fornecidas nos documentos de contexto
- Se não houver informação suficiente, diga que não possui essa informação específica
- Seja cordial, profissional e útil
- Cite as fontes quando relevante
- Mantenha o foco em questões relacionadas ao mandato e serviços públicos

`;

    // Adicionar contexto da conversa
    if (conversationContext && conversationContext.recent_messages.length > 0) {
      prompt += `CONTEXTO DA CONVERSA ANTERIOR:\n`;
      conversationContext.recent_messages.slice(-3).forEach(msg => {
        prompt += `${msg.role === 'user' ? 'Cidadão' : 'Assistente'}: ${msg.content}\n`;
      });
      prompt += `\n`;
    }

    // Adicionar documentos relevantes
    if (searchResults.length > 0) {
      prompt += `DOCUMENTOS RELEVANTES:\n`;
      searchResults.forEach((result, index) => {
        prompt += `[Documento ${index + 1}]: ${result.chunk_text}\n\n`;
      });
    } else {
      prompt += `ATENÇÃO: Não foram encontrados documentos específicos para esta consulta.\n\n`;
    }

    prompt += `PERGUNTA DO CIDADÃO: ${query}\n`;

    return prompt;
  }

  async generateResponse(prompt) {
    try {
      const result = await this.model.generateContent(prompt);
      const response = await result.response.text();
      return response;
    } catch (error) {
      console.error('❌ Erro ao gerar resposta com Gemini:', error);
      throw error;
    }
  }

  async getConversationContext(conversationId) {
    try {
      const { data, error } = await this.supabase
        .from('whatsapp_conversations')
        .select('recent_messages')
        .eq('id', conversationId)
        .single();

      if (error) {
        console.error('❌ Erro ao obter contexto da conversa:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('❌ Erro ao obter contexto da conversa:', error);
      throw error;
    }
  }

  async saveConversationMessage(conversationId, role, content, sources = []) {
    try {
      const { data, error } = await this.supabase
        .from('conversations')
        .upsert([
          {
            id: conversationId,
            recent_messages: [
              { role, content, timestamp: new Date().toISOString(), sources }
            ]
          }
        ], { onConflict: ['id'] })
        .select();

      if (error) {
        console.error('❌ Erro ao salvar mensagem na conversa:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('❌ Erro ao salvar mensagem na conversa:', error);
      throw error;
    }
  }

  calculateConfidenceScore(searchResults) {
    // Implementar lógica para calcular a confiança
    return searchResults.length > 0 ? 0.8 : 0.2; // Valor padrão
  }

  /**
   * Gerar resposta simulada quando VectorStore não está disponível
   */
  generateSimulatedResponse(userMessage) {
    const responses = [
      `Olá! Sou a Vereadora Rafaela de Nilda. Como posso ajudá-lo hoje? 🏛️`,
      `Obrigada por entrar em contato! Estou aqui para servir Parnamirim. Em que posso ajudar? 💖`,
      `Oi! É um prazer falar com você. Como posso contribuir para melhorar nossa cidade? 🤩`,
      `Olá, querido(a)! Conte comigo para o que precisar. Estamos juntos por Parnamirim! 🙏🏽`
    ];

    const randomResponse = responses[Math.floor(Math.random() * responses.length)];

    return {
      response: randomResponse,
      sources: [],
      metadata: {
        mode: 'simulated',
        vereadora: 'Rafaela de Nilda',
        municipio: 'Parnamirim/RN',
        timestamp: new Date().toISOString(),
        processingTime: Math.random() * 500 + 200,
        query: userMessage
      }
    };
  }

  /**
   * Health check do serviço
   */
  async healthCheck() {
    try {
      if (!this.isInitialized) {
        return false;
      }

      return true; // Sempre retornar true se inicializado
    } catch (error) {
      this.logger.warn('⚠️ Health check falhou:', error.message);
      return false;
    }
  }

  /**
   * Processar documento para upload
   */
  async processDocument(fileInfo) {
    try {
      this.logger.info(`🔄 Processando documento: ${fileInfo.name}`);

      const documentId = Date.now().toString();

      // Extrair texto real do PDF
      let extractedText = '';
      let chunks = [];

      if (fileInfo.type === 'application/pdf') {
        try {
          // Ler o arquivo PDF real
          extractedText = await this.extractTextFromPDF(fileInfo.path);
          this.logger.info(`📄 Texto extraído: ${extractedText.length} caracteres`);

          if (extractedText.trim().length === 0) {
            this.logger.warn('⚠️ PDF não contém texto extraível, usando placeholder');
            extractedText = this.simulateTextExtraction(fileInfo.name);
          }
        } catch (pdfError) {
          this.logger.warn('⚠️ Erro ao extrair texto do PDF, usando placeholder:', pdfError.message);
          extractedText = this.simulateTextExtraction(fileInfo.name);
        }

        chunks = this.createChunks(extractedText, documentId, fileInfo.name);
      } else {
        throw new Error('Tipo de arquivo não suportado. Apenas PDFs são aceitos.');
      }

      const document = {
        id: documentId,
        name: fileInfo.name,
        path: fileInfo.path,
        size: fileInfo.size,
        type: fileInfo.type,
        status: 'ready',
        upload_date: new Date().toISOString(),
        chunks_count: chunks.length,
        extracted_text: extractedText,
        chunks: chunks,
        metadata: {
          pages: Math.ceil(chunks.length / 3), // Aproximadamente 3 chunks por página
          language: 'pt-BR',
          extraction_method: extractedText.includes('RELATÓRIO DE ATIVIDADES MUNICIPAIS') ? 'simulated' : 'real'
        }
      };

      // Adicionar ao cache
      this.documents.set(documentId, document);

      // Salvar no Supabase se disponível
      try {
        if (this.supabase.isReady()) {
          await this.supabase.saveDocument({
            id: documentId,
            name: fileInfo.name,
            original_name: fileInfo.name,
            path: fileInfo.path,
            size: fileInfo.size,
            type: fileInfo.type,
            status: document.status,
            extracted_text: extractedText,
            chunks_count: chunks.length,
            metadata: document.metadata
          });

          await this.supabase.saveDocumentChunks(documentId, chunks);
          this.logger.info(`💾 Documento salvo no Supabase: ${fileInfo.name}`);
        }
      } catch (supabaseError) {
        this.logger.warn('⚠️ Erro ao salvar no Supabase:', supabaseError.message);
        // Continuar mesmo se o Supabase falhar
      }

      this.logger.info(`✅ Documento processado: ${fileInfo.name} (${chunks.length} chunks)`);
      return { documentId };

    } catch (error) {
      this.logger.error('❌ Erro ao processar documento:', error);
      throw error;
    }
  }

  /**
   * Obter lista de documentos
   */
  async getDocuments() {
    try {
      return Array.from(this.documents.values());
    } catch (error) {
      this.logger.error('❌ Erro ao obter documentos:', error);
      return [];
    }
  }

  /**
   * Remover documento
   */
  async removeDocument(documentId) {
    try {
      if (this.documents.has(documentId)) {
        this.documents.delete(documentId);
        this.logger.info(`✅ Documento removido: ${documentId}`);
      } else {
        throw new Error('Documento não encontrado');
      }
    } catch (error) {
      this.logger.error('❌ Erro ao remover documento:', error);
      throw error;
    }
  }

  /**
   * Extrair texto real do PDF
   */
  async extractTextFromPDF(filePath) {
    try {
      this.logger.info(`📖 Extraindo texto do PDF: ${filePath}`);

      // Verificar se o arquivo existe
      if (!fs.existsSync(filePath)) {
        throw new Error(`Arquivo não encontrado: ${filePath}`);
      }

      // Verificar tamanho do arquivo
      const stats = fs.statSync(filePath);
      this.logger.info(`📊 Tamanho do arquivo: ${stats.size} bytes`);

      if (stats.size === 0) {
        throw new Error('Arquivo PDF está vazio');
      }

      // Importar pdf-parse dinamicamente com configuração específica
      const { default: pdfParse } = await import('pdf-parse');

      // Ler o arquivo PDF
      const dataBuffer = fs.readFileSync(filePath);

      // Configurações para pdf-parse
      const options = {
        // Desabilitar renderização de imagens para evitar problemas
        max: 0,
        version: 'v1.10.100'
      };

      // Extrair texto usando pdf-parse com configurações específicas
      const data = await pdfParse(dataBuffer, options);

      this.logger.info(`✅ Texto extraído com sucesso: ${data.text.length} caracteres, ${data.numpages} páginas`);

      // Verificar se o texto foi extraído e é legível
      if (!data.text || data.text.trim().length === 0) {
        throw new Error('PDF não contém texto extraível ou está protegido');
      }

      const trimmedText = data.text.trim();

      // Verificar se o texto extraído é legível (não é código binário)
      if (!this.isReadableText(trimmedText)) {
        throw new Error('PDF contém texto codificado/binário que não pode ser processado');
      }

      return trimmedText;
    } catch (error) {
      this.logger.error('❌ Erro ao extrair texto do PDF:', error);

      // Se for erro específico do pdf-parse, tentar método alternativo
      if (error.message.includes('05-versions-space.pdf') || error.code === 'ENOENT') {
        this.logger.warn('⚠️ Erro interno do pdf-parse, tentando método alternativo...');
        return await this.extractTextAlternative(filePath);
      }

      throw error;
    }
  }

  /**
   * Método alternativo para extrair texto do PDF
   */
  async extractTextAlternative(filePath) {
    try {
      this.logger.info(`🔄 Tentando método alternativo para: ${filePath}`);

      // Verificar se é realmente um PDF válido
      const dataBuffer = fs.readFileSync(filePath);
      const header = dataBuffer.toString('ascii', 0, 4);

      if (header !== '%PDF') {
        throw new Error('Arquivo não é um PDF válido');
      }

      // Tentar extrair texto básico procurando por padrões de texto
      const content = dataBuffer.toString('binary');
      const textMatches = content.match(/\(([^)]+)\)/g);

      if (textMatches && textMatches.length > 0) {
        const extractedText = textMatches
          .map(match => match.slice(1, -1))
          .join(' ')
          .replace(/\\[rn]/g, ' ')
          .replace(/\s+/g, ' ')
          .trim();

        // Verificar se o texto extraído é legível (não é código binário)
        if (extractedText.length > 10 && this.isReadableText(extractedText)) {
          this.logger.info(`✅ Texto extraído via método alternativo: ${extractedText.length} caracteres`);
          return extractedText;
        }
      }

      throw new Error('Não foi possível extrair texto legível do PDF usando métodos alternativos');
    } catch (error) {
      this.logger.error('❌ Erro no método alternativo:', error);
      throw error;
    }
  }

  /**
   * Detectar se a pergunta é sobre um documento específico
   */
  detectSpecificDocument(query) {
    const queryLower = query.toLowerCase();

    // Padrões para detectar referência a documento específico
    const documentPatterns = [
      /(?:documento|arquivo|pdf)\s+(?:final|001|002|003|\d+)/gi,
      /(?:documento|arquivo)\s+["']([^"']+)["']/gi,
      /(?:de que se trata|sobre|conteúdo do?)\s+(?:o\s+)?(?:documento|arquivo|pdf)\s+([^\s?.,!]+)/gi,
      /(?:documento|arquivo)\s+([a-zA-Z0-9\s_-]+\.pdf)/gi
    ];

    for (const pattern of documentPatterns) {
      const matches = [...queryLower.matchAll(pattern)];
      if (matches.length > 0) {
        // Extrair nome do documento da primeira captura
        const match = matches[0];
        let documentName = match[1] || match[0];

        // Limpar e normalizar o nome
        documentName = documentName
          .replace(/^(documento|arquivo|pdf)\s+/gi, '')
          .replace(/[?.,!]/g, '')
          .trim();

        // Se não termina com .pdf, adicionar
        if (!documentName.toLowerCase().endsWith('.pdf')) {
          documentName += '.pdf';
        }

        console.log(`🎯 Documento específico detectado: "${documentName}"`);
        return documentName;
      }
    }

    return null;
  }

  /**
   * Verificar se o texto extraído é legível (não é código binário)
   */
  isReadableText(text) {
    // Verificar se o texto contém principalmente caracteres legíveis
    const readableChars = text.match(/[a-zA-Z0-9\s\.,;:!?\-()]/g);
    const readableRatio = readableChars ? readableChars.length / text.length : 0;

    // Se menos de 70% dos caracteres são legíveis, considerar como código binário
    if (readableRatio < 0.7) {
      this.logger.warn(`⚠️ Texto parece ser código binário (${Math.round(readableRatio * 100)}% legível)`);
      return false;
    }

    // Verificar se contém muitos caracteres de controle ou metadados PDF
    const binaryPatterns = [
      /endobj|stream|xref|trailer/gi,  // Palavras-chave PDF
      /[^\x20-\x7E\s]/g,               // Caracteres não ASCII imprimíveis
      /\d+\s+\d+\s+R/g,                // Referências de objeto PDF
      /\/[A-Z][a-zA-Z]+/g              // Comandos PDF como /Type, /Page
    ];

    let binaryScore = 0;
    binaryPatterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        binaryScore += matches.length;
      }
    });

    // Se há muitos padrões binários, considerar como não legível
    if (binaryScore > text.length * 0.1) {
      this.logger.warn(`⚠️ Texto contém muitos padrões binários (score: ${binaryScore})`);
      return false;
    }

    return true;
  }

  /**
   * Simular extração de texto do PDF (fallback quando extração real falha)
   */
  simulateTextExtraction(filename) {
    // ❌ REMOVIDO: Dados simulados que causavam confusão
    // O sistema agora deve processar apenas documentos reais

    // Para PDFs que não conseguimos extrair texto, vamos criar um placeholder
    // que indica que o documento foi carregado mas precisa de descrição manual
    const placeholderText = `DOCUMENTO PDF CARREGADO: ${filename}

Este documento foi carregado com sucesso, mas o texto não pôde ser extraído automaticamente.
Isso pode acontecer quando o PDF:
- Está protegido por senha
- Usa codificação especial
- Contém principalmente imagens
- Tem formato não padrão

Para que eu possa responder perguntas sobre este documento, você pode:
1. Descrever o conteúdo do documento
2. Fazer perguntas específicas sobre o que você sabe que está no documento
3. Tentar fazer upload de uma versão diferente do arquivo

Nome do arquivo: ${filename}
Status: Carregado (texto não extraído)
Data: ${new Date().toLocaleString('pt-BR')}`;

    return placeholderText;
  }

  /**
   * Criar chunks de texto
   */
  createChunks(text, documentId, documentName) {
    const chunks = [];
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const chunkSize = Math.ceil(sentences.length / 5); // Dividir em aproximadamente 5 chunks

    for (let i = 0; i < sentences.length; i += chunkSize) {
      const chunkSentences = sentences.slice(i, i + chunkSize);
      const chunkText = chunkSentences.join('. ').trim() + '.';

      chunks.push({
        id: `${documentId}_chunk_${chunks.length}`,
        content: chunkText,
        metadata: {
          document_id: documentId,
          document_name: documentName,
          chunk_index: chunks.length,
          page: Math.floor(chunks.length / 3) + 1,
          word_count: chunkText.split(' ').length
        },
        document_id: documentId,
        embedding: null
      });
    }

    return chunks;
  }

  /**
   * Obter chunks de documentos
   */
  async getChunks() {
    try {
      let chunks = [];

      // Tentar buscar do Supabase primeiro
      if (this.supabase.isReady()) {
        try {
          const supabaseChunks = await this.supabase.getDocumentChunks();
          if (supabaseChunks.length > 0) {
            this.logger.info(`📊 ${supabaseChunks.length} chunks carregados do Supabase`);
            return supabaseChunks.map(chunk => ({
              id: chunk.id,
              content: chunk.content,
              metadata: chunk.metadata || {},
              document_id: chunk.document_id,
              embedding: chunk.embedding
            }));
          }
        } catch (supabaseError) {
          this.logger.warn('⚠️ Erro ao buscar chunks do Supabase:', supabaseError.message);
        }
      }

      // Fallback para cache local
      for (const [docId, document] of this.documents.entries()) {
        if (document.status === 'ready' && document.chunks) {
          // Usar chunks reais se disponíveis
          chunks.push(...document.chunks);
        } else if (document.status === 'ready') {
          // Fallback para chunks simulados se não houver chunks reais
          for (let i = 0; i < document.chunks_count; i++) {
            chunks.push({
              id: `${docId}_chunk_${i}`,
              content: `Conteúdo do chunk ${i + 1} do documento ${document.name}. Este é um texto simulado que representa o conteúdo extraído do documento PDF.`,
              metadata: {
                document_id: docId,
                document_name: document.name,
                chunk_index: i,
                page: Math.floor(i / 3) + 1
              },
              document_id: docId,
              embedding: null
            });
          }
        }
      }

      this.logger.info(`📊 ${chunks.length} chunks carregados do cache local`);
      return chunks;
    } catch (error) {
      this.logger.error('❌ Erro ao obter chunks:', error);
      return [];
    }
  }

  /**
   * Obter status do serviço
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      vectorStore: !!this.vectorStore,
      llm: !!this.llm,
      documentsCount: this.documents.size,
      timestamp: new Date().toISOString()
    };
  }
}