# 🗄️ **CONFIGURAÇÃO DO SUPABASE**

Guia completo para configurar o banco de dados Supabase para o sistema RAG da Vereadora Rafaela.

## 📋 **PRÉ-REQUISITOS**

1. **Conta no Supabase**: [https://supabase.com](https://supabase.com)
2. **Projeto criado** no Supabase
3. **Acesso ao SQL Editor** do projeto

## 🚀 **PASSO A PASSO**

### **1. Criar Projeto no Supabase**

1. Acesse [https://supabase.com/dashboard](https://supabase.com/dashboard)
2. Clique em **"New Project"**
3. Escolha sua organização
4. Configure:
   - **Name**: `vereadora-rafaela-rag`
   - **Database Password**: Crie uma senha forte
   - **Region**: `South America (São Paulo)` (recomendado para Brasil)
5. Clique em **"Create new project"**
6. Aguarde a criação (pode levar alguns minutos)

### **2. Executar o Schema SQL**

1. No dashboard do projeto, vá para **"SQL Editor"**
2. Clique em **"New query"**
3. Copie todo o conteúdo do arquivo `supabase-schema.sql`
4. Cole no editor SQL
5. Clique em **"Run"** (ou pressione Ctrl+Enter)
6. Verifique se todas as tabelas foram criadas sem erros

### **3. Verificar Tabelas Criadas**

No **Table Editor**, você deve ver as seguintes tabelas:
- ✅ `documents` - Documentos PDF
- ✅ `document_chunks` - Fragmentos de texto
- ✅ `whatsapp_sessions` - Sessões WhatsApp
- ✅ `whatsapp_conversations` - Conversas
- ✅ `whatsapp_messages` - Mensagens
- ✅ `rag_interactions` - Interações RAG
- ✅ `analytics_logs` - Logs e analytics
- ✅ `system_config` - Configurações

### **4. Obter Credenciais**

1. Vá para **"Settings" > "API"**
2. Copie as seguintes informações:
   - **Project URL**: `https://seu-projeto.supabase.co`
   - **anon public**: Chave pública (para frontend)
   - **service_role**: Chave de serviço (para backend)

### **5. Configurar Variáveis de Ambiente**

No arquivo `.env` do backend, configure:

```env
# SUPABASE
SUPABASE_URL=https://seu-projeto.supabase.co
SUPABASE_ANON_KEY=sua-chave-anonima-aqui
SUPABASE_SERVICE_ROLE_KEY=sua-chave-service-role-aqui
```

### **6. Habilitar Extensão Vector (Opcional)**

Para busca semântica avançada com embeddings:

```sql
-- Execute no SQL Editor
CREATE EXTENSION IF NOT EXISTS vector;
```

### **7. Configurar RLS (Row Level Security)**

As políticas RLS já estão configuradas no schema, mas você pode ajustá-las:

1. Vá para **"Authentication" > "Policies"**
2. Verifique as políticas criadas
3. Ajuste conforme necessário

## 🔧 **CONFIGURAÇÕES AVANÇADAS**

### **Configurar Webhook (Opcional)**

Para receber notificações em tempo real:

1. Vá para **"Database" > "Webhooks"**
2. Crie webhook para tabela `whatsapp_messages`
3. URL: `http://seu-backend:3001/api/webhook/supabase`

### **Configurar Backup Automático**

1. Vá para **"Settings" > "Database"**
2. Configure **"Point in Time Recovery"**
3. Ative backups automáticos

## 🧪 **TESTAR CONEXÃO**

### **1. Teste via SQL Editor**

```sql
-- Inserir configuração de teste
INSERT INTO system_config (config_key, config_value, description) 
VALUES ('test_connection', '"success"', 'Teste de conexão');

-- Verificar se foi inserido
SELECT * FROM system_config WHERE config_key = 'test_connection';
```

### **2. Teste via Backend**

1. Configure as variáveis de ambiente
2. Reinicie o servidor backend
3. Verifique os logs para:
   ```
   ✅ Supabase conectado com sucesso
   ```

## 📊 **MONITORAMENTO**

### **Dashboard do Supabase**

- **Database**: Monitore uso e performance
- **Auth**: Usuários e sessões
- **Storage**: Arquivos (se usar)
- **Edge Functions**: Funções serverless (se usar)

### **Logs e Métricas**

- Acesse **"Logs"** para ver queries e erros
- Configure alertas para problemas
- Monitore uso de recursos

## 🔒 **SEGURANÇA**

### **Configurações Recomendadas**

1. **RLS Habilitado**: ✅ Já configurado
2. **Políticas Restritivas**: ✅ Já configurado
3. **HTTPS Only**: ✅ Padrão do Supabase
4. **API Keys Seguras**: ⚠️ Mantenha em segredo

### **Boas Práticas**

- ✅ Use `SUPABASE_ANON_KEY` no frontend
- ✅ Use `SUPABASE_SERVICE_ROLE_KEY` no backend
- ❌ Nunca exponha service_role no frontend
- ✅ Configure CORS adequadamente
- ✅ Use variáveis de ambiente

## 🚨 **TROUBLESHOOTING**

### **Erro: "relation does not exist"**
- Verifique se o schema foi executado completamente
- Confirme se está no schema correto (`public`)

### **Erro: "permission denied"**
- Verifique as políticas RLS
- Confirme se está usando a chave correta

### **Erro: "connection refused"**
- Verifique a URL do projeto
- Confirme se o projeto está ativo

### **Performance Lenta**
- Verifique os índices criados
- Monitore queries no dashboard
- Considere otimizar consultas

## 📞 **SUPORTE**

- **Documentação**: [https://supabase.com/docs](https://supabase.com/docs)
- **Discord**: [https://discord.supabase.com](https://discord.supabase.com)
- **GitHub**: [https://github.com/supabase/supabase](https://github.com/supabase/supabase)

---

## ✅ **CHECKLIST FINAL**

- [ ] Projeto Supabase criado
- [ ] Schema SQL executado
- [ ] Tabelas verificadas
- [ ] Credenciais copiadas
- [ ] Variáveis de ambiente configuradas
- [ ] Conexão testada
- [ ] Backend funcionando
- [ ] Logs verificados

**🎉 Supabase configurado com sucesso!**
