#!/usr/bin/env node

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = path.join(__dirname, '..');

console.log('🏛️ Configurando Backend WhatsApp - Vereadora Rafaela de Nilda\n');

async function setup() {
  try {
    // 1. Criar diretórios necessários
    await createDirectories();
    
    // 2. Verificar arquivo .env
    await checkEnvironmentFile();
    
    // 3. Criar arquivos de configuração
    await createConfigFiles();
    
    // 4. Verificar dependências
    await checkDependencies();
    
    console.log('✅ Setup concluído com sucesso!\n');
    console.log('📋 Próximos passos:');
    console.log('1. Configure o arquivo .env com suas informações');
    console.log('2. Execute: npm run dev');
    console.log('3. Escaneie o QR Code com WhatsApp');
    console.log('4. Teste enviando uma mensagem\n');
    
  } catch (error) {
    console.error('❌ Erro durante o setup:', error);
    process.exit(1);
  }
}

async function createDirectories() {
  console.log('📁 Criando diretórios...');
  
  const directories = [
    'data',
    'data/sessions',
    'data/backups',
    'logs',
    'public',
    'temp'
  ];
  
  for (const dir of directories) {
    const dirPath = path.join(rootDir, dir);
    try {
      await fs.mkdir(dirPath, { recursive: true });
      console.log(`  ✓ ${dir}`);
    } catch (error) {
      console.log(`  ⚠️ ${dir} (já existe)`);
    }
  }
  
  console.log('');
}

async function checkEnvironmentFile() {
  console.log('⚙️ Verificando arquivo de ambiente...');
  
  const envPath = path.join(rootDir, '.env');
  const envExamplePath = path.join(rootDir, '.env.example');
  
  try {
    await fs.access(envPath);
    console.log('  ✓ Arquivo .env encontrado');
  } catch (error) {
    console.log('  📝 Criando arquivo .env a partir do exemplo...');
    try {
      const envExample = await fs.readFile(envExamplePath, 'utf8');
      await fs.writeFile(envPath, envExample);
      console.log('  ✓ Arquivo .env criado');
    } catch (error) {
      console.log('  ❌ Erro ao criar .env:', error.message);
    }
  }
  
  console.log('');
}

async function createConfigFiles() {
  console.log('📄 Criando arquivos de configuração...');
  
  // Criar .gitignore se não existir
  const gitignorePath = path.join(rootDir, '.gitignore');
  const gitignoreContent = `# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log

# Data
data/sessions/
data/backups/
temp/

# WhatsApp
tokens/
.wwebjs_auth/
.wwebjs_cache/

# OS
.DS_Store
Thumbs.db

# IDE
.vscode/
.idea/
*.swp
*.swo

# Build
dist/
build/

# QR Codes
public/qr-code.png
`;

  try {
    await fs.access(gitignorePath);
    console.log('  ✓ .gitignore já existe');
  } catch (error) {
    await fs.writeFile(gitignorePath, gitignoreContent);
    console.log('  ✓ .gitignore criado');
  }

  // Criar nodemon.json
  const nodemonPath = path.join(rootDir, 'nodemon.json');
  const nodemonConfig = {
    "watch": ["src", "server-centralized.js"],
    "ext": "js,json",
    "ignore": ["data/", "logs/", "public/", "temp/"],
    "exec": "node server-centralized.js",
    "env": {
      "NODE_ENV": "development"
    }
  };

  try {
    await fs.access(nodemonPath);
    console.log('  ✓ nodemon.json já existe');
  } catch (error) {
    await fs.writeFile(nodemonPath, JSON.stringify(nodemonConfig, null, 2));
    console.log('  ✓ nodemon.json criado');
  }

  console.log('');
}

async function checkDependencies() {
  console.log('📦 Verificando dependências...');
  
  try {
    const packageJsonPath = path.join(rootDir, 'package.json');
    const packageJson = JSON.parse(await fs.readFile(packageJsonPath, 'utf8'));
    
    const requiredDeps = [
      '@wppconnect-team/wppconnect',
      'express',
      'cors',
      'helmet',
      'dotenv',
      'axios',
      'winston',
      'moment'
    ];
    
    const missingDeps = [];
    
    for (const dep of requiredDeps) {
      if (!packageJson.dependencies[dep]) {
        missingDeps.push(dep);
      } else {
        console.log(`  ✓ ${dep}`);
      }
    }
    
    if (missingDeps.length > 0) {
      console.log('\n  ⚠️ Dependências faltando:');
      missingDeps.forEach(dep => console.log(`    - ${dep}`));
      console.log('\n  Execute: npm install');
    }
    
  } catch (error) {
    console.log('  ❌ Erro ao verificar dependências:', error.message);
  }
  
  console.log('');
}

// Executar setup se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  setup();
}

export { setup };
