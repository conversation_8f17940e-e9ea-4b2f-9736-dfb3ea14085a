import { Router } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs/promises';

const router = Router();

// Configurar multer para upload de arquivos
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'data', 'uploads');
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    // Gerar nome único para o arquivo
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    const name = path.basename(file.originalname, ext);
    cb(null, `${name}-${uniqueSuffix}${ext}`);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
  },
  fileFilter: (req, file, cb) => {
    // Aceitar apenas PDFs
    if (file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('Apenas arquivos PDF são permitidos'), false);
    }
  }
});

// POST /api/documents/upload - Upload de documento
router.post('/upload', upload.single('document'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'Nenhum arquivo foi enviado'
      });
    }

    const ragService = req.ragService;
    if (!ragService) {
      return res.status(500).json({
        success: false,
        error: 'Serviço RAG não disponível'
      });
    }

    // Processar o documento através do RAG Service
    const result = await ragService.processDocument({
      name: req.file.originalname,
      path: req.file.path,
      size: req.file.size,
      type: req.file.mimetype
    });

    res.json({
      success: true,
      message: 'Documento enviado com sucesso',
      document: {
        id: result.documentId || Date.now().toString(),
        name: req.file.originalname,
        size: req.file.size,
        type: req.file.mimetype,
        status: 'processing',
        upload_date: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Erro no upload:', error);
    
    // Limpar arquivo em caso de erro
    if (req.file) {
      try {
        await fs.unlink(req.file.path);
      } catch (unlinkError) {
        console.error('Erro ao remover arquivo:', unlinkError);
      }
    }

    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor',
      details: error.message
    });
  }
});

// GET /api/documents - Listar documentos
router.get('/', async (req, res) => {
  try {
    const ragService = req.ragService;
    if (!ragService) {
      return res.status(500).json({
        success: false,
        error: 'Serviço RAG não disponível'
      });
    }

    // Obter lista de documentos do RAG Service
    const documents = await ragService.getDocuments() || [];

    res.json({
      success: true,
      documents: documents.map(doc => ({
        id: doc.id,
        name: doc.name || doc.title,
        size: doc.size || 0,
        type: doc.type || 'application/pdf',
        status: doc.status || 'ready',
        upload_date: doc.upload_date || doc.created_at,
        chunks_count: doc.chunks_count || 0
      }))
    });

  } catch (error) {
    console.error('Erro ao listar documentos:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor',
      details: error.message
    });
  }
});

// DELETE /api/documents/:id - Remover documento
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const ragService = req.ragService;
    
    if (!ragService) {
      return res.status(500).json({
        success: false,
        error: 'Serviço RAG não disponível'
      });
    }

    // Remover documento do RAG Service
    await ragService.removeDocument(id);

    res.json({
      success: true,
      message: 'Documento removido com sucesso'
    });

  } catch (error) {
    console.error('Erro ao remover documento:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor',
      details: error.message
    });
  }
});

export default router;
