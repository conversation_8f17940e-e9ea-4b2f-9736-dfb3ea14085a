import { Router } from 'express';

const router = Router();

// POST /api/analytics/log - Registrar interação/evento
router.post('/log', async (req, res) => {
  try {
    const { type, timestamp, metadata } = req.body;

    // Validar dados obrigatórios
    if (!type) {
      return res.status(400).json({
        success: false,
        error: 'Tipo de evento é obrigatório'
      });
    }

    // Tipos válidos de eventos
    const validTypes = ['message_sent', 'message_received', 'document_upload', 'error'];
    if (!validTypes.includes(type)) {
      return res.status(400).json({
        success: false,
        error: `Tipo de evento inválido. Tipos válidos: ${validTypes.join(', ')}`
      });
    }

    // Criar log de interação
    const logEntry = {
      id: Date.now().toString(),
      type,
      timestamp: timestamp || new Date().toISOString(),
      metadata: metadata || {},
      ip: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent'),
      created_at: new Date().toISOString()
    };

    // Log no console para desenvolvimento
    console.log('📊 Analytics Log:', {
      type: logEntry.type,
      timestamp: logEntry.timestamp,
      metadata: logEntry.metadata
    });

    // Tentar salvar no serviço de persistência se disponível
    try {
      const persistenceService = req.persistenceService;
      if (persistenceService) {
        await persistenceService.saveAnalyticsLog(logEntry);
      }
    } catch (persistenceError) {
      console.warn('⚠️ Erro ao salvar no serviço de persistência:', persistenceError.message);
      // Não falhar a requisição por erro de persistência
    }

    res.json({
      success: true,
      message: 'Evento registrado com sucesso',
      logId: logEntry.id
    });

  } catch (error) {
    console.error('Erro ao registrar evento analytics:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor',
      details: error.message
    });
  }
});

// GET /api/analytics/stats - Estatísticas básicas
router.get('/stats', async (req, res) => {
  try {
    // Estatísticas básicas do sistema
    const stats = {
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      version: '2.0.0'
    };

    // Tentar obter estatísticas dos serviços se disponível
    try {
      const serviceManager = req.serviceManager;
      if (serviceManager) {
        stats.services = serviceManager.getServicesStatus();
      }
    } catch (serviceError) {
      console.warn('⚠️ Erro ao obter status dos serviços:', serviceError.message);
    }

    res.json({
      success: true,
      stats
    });

  } catch (error) {
    console.error('Erro ao obter estatísticas:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor',
      details: error.message
    });
  }
});

// GET /api/analytics/logs - Obter logs (limitado)
router.get('/logs', async (req, res) => {
  try {
    const { limit = 50, type, since } = req.query;

    // Em desenvolvimento, retornar logs simulados
    const mockLogs = [
      {
        id: '1',
        type: 'message_sent',
        timestamp: new Date().toISOString(),
        metadata: { recipient: 'user123', message_length: 45 }
      },
      {
        id: '2',
        type: 'document_upload',
        timestamp: new Date(Date.now() - 60000).toISOString(),
        metadata: { filename: 'documento.pdf', size: 1024 }
      }
    ];

    // Filtrar por tipo se especificado
    let filteredLogs = mockLogs;
    if (type) {
      filteredLogs = mockLogs.filter(log => log.type === type);
    }

    // Filtrar por data se especificado
    if (since) {
      const sinceDate = new Date(since);
      filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) >= sinceDate);
    }

    // Limitar resultados
    const limitedLogs = filteredLogs.slice(0, parseInt(limit));

    res.json({
      success: true,
      logs: limitedLogs,
      total: filteredLogs.length,
      limit: parseInt(limit)
    });

  } catch (error) {
    console.error('Erro ao obter logs:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor',
      details: error.message
    });
  }
});

export default router;
