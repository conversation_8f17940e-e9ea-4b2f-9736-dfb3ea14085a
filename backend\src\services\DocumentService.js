import VectorStore from '../utils/vectorStore.js';
import { textExtractor } from '../utils/textExtractor.js';
import { createClient } from '@supabase/supabase-js';
import { GoogleGenerativeAI } from '@google/generative-ai';
import fs from 'fs/promises';
import path from 'path';

// Importação dinâmica para evitar problemas de inicialização
let pdf = null;

export class DocumentService {
  constructor() {
    this.supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_KEY
    );
    this.genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    // Modelo para geração de texto
    this.textModel = this.genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
    // Modelo para embeddings (usar text-embedding-004 ou embedding-001)
    this.embeddingModel = this.genAI.getGenerativeModel({ model: "text-embedding-004" });
    this.vectorStore = new VectorStore();
  }

  async uploadDocument(file) {
    try {
      const text = await textExtractor.extract(file);
      const chunks = this.splitIntoChunks(text);
      
      const documentId = await this.saveDocumentMetadata(file);
      
      // Adicionar chunks ao vector store
      for (const chunk of chunks) {
        await this.vectorStore.addDocument(chunk, {
          documentId,
          fileName: file.name
        });
      }

      return { success: true, documentId };
    } catch (error) {
      this.logger.error('Erro ao fazer upload do documento:', error);
      throw error;
    }
  }

  /**
   * Processa um documento PDF e cria chunks para RAG
   */
  async processDocument(filePath, metadata = {}) {
    try {
      console.log(`📄 Iniciando processamento do documento: ${filePath}`);
      
      // 1. Extrair texto do PDF
      const pdfBuffer = await fs.readFile(filePath);
      const pdfData = await pdf(pdfBuffer);
      const content = pdfData.text;

      // 2. Salvar documento no banco
      const { data: document, error: docError } = await this.supabase
        .from('documents')
        .insert({
          title: metadata.title || path.basename(filePath),
          content: content,
          file_path: filePath,
          file_type: 'pdf',
          file_size: pdfBuffer.length,
          status: 'processing',
          metadata: metadata
        })
        .select()
        .single();

      if (docError) throw docError;

      // 3. Dividir em chunks
      const chunks = this.createChunks(content, {
        chunkSize: 1000,
        overlap: 200
      });

      // 4. Gerar embeddings e salvar chunks
      await this.processChunks(document.id, chunks);

      // 5. Atualizar status do documento
      await this.supabase
        .from('documents')
        .update({ 
          status: 'completed', 
          processed_at: new Date().toISOString() 
        })
        .eq('id', document.id);

      console.log(`✅ Documento processado com sucesso: ${document.id}`);
      return document;

    } catch (error) {
      console.error('❌ Erro ao processar documento:', error);
      throw error;
    }
  }

  /**
   * Divide o texto em chunks menores
   */
  createChunks(text, options = {}) {
    const { chunkSize = 1000, overlap = 200 } = options;
    const chunks = [];
    
    // Dividir por parágrafos primeiro
    const paragraphs = text.split(/\n\s*\n/);
    let currentChunk = '';
    let chunkIndex = 0;

    for (const paragraph of paragraphs) {
      if (currentChunk.length + paragraph.length > chunkSize && currentChunk.length > 0) {
        chunks.push({
          text: currentChunk.trim(),
          index: chunkIndex++
        });
        
        // Manter overlap
        const words = currentChunk.split(' ');
        const overlapWords = words.slice(-Math.floor(overlap / 5)); // Aproximadamente
        currentChunk = overlapWords.join(' ') + ' ' + paragraph;
      } else {
        currentChunk += (currentChunk ? '\n\n' : '') + paragraph;
      }
    }

    // Adicionar último chunk
    if (currentChunk.trim()) {
      chunks.push({
        text: currentChunk.trim(),
        index: chunkIndex
      });
    }

    return chunks;
  }

  /**
   * Processa chunks e gera embeddings
   */
  async processChunks(documentId, chunks) {
    const batchSize = 5; // Processar em lotes para evitar rate limits
    
    for (let i = 0; i < chunks.length; i += batchSize) {
      const batch = chunks.slice(i, i + batchSize);
      
      await Promise.all(batch.map(async (chunk) => {
        try {
          // Gerar embedding usando Gemini
          const embedding = await this.generateEmbedding(chunk.text);
          
          // Salvar chunk no banco
          await this.supabase
            .from('document_chunks')
            .insert({
              document_id: documentId,
              chunk_text: chunk.text,
              chunk_index: chunk.index,
              embedding: embedding,
              metadata: {
                word_count: chunk.text.split(' ').length,
                char_count: chunk.text.length
              }
            });
            
        } catch (error) {
          console.error(`❌ Erro ao processar chunk ${chunk.index}:`, error);
        }
      }));
      
      // Pequena pausa entre lotes
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  /**
   * Gera embedding para um texto usando Gemini
   */
  async generateEmbedding(text) {
    try {
      // Usar modelo específico para embeddings
      const result = await this.embeddingModel.embedContent(text);
      return result.embedding.values;
    } catch (error) {
      console.error('❌ Erro ao gerar embedding:', error);
      // Fallback: retornar embedding simulado
      console.warn('⚠️ Usando embedding simulado');
      return Array(384).fill(0).map(() => Math.random() - 0.5);
    }
  }

  /**
   * Busca semântica nos documentos
   */
  async semanticSearch(query, options = {}) {
    const { limit = 5, threshold = 0.7, documentFilter } = options;

    try {
      console.log(`🔍 Busca semântica: "${query}" ${documentFilter ? `(filtro: ${documentFilter})` : ''}`);

      // Se há filtro por documento específico, buscar apenas chunks desse documento
      if (documentFilter) {
        return await this.searchInSpecificDocument(query, documentFilter, { limit, threshold });
      }

      // 1. Gerar embedding da query
      const queryEmbedding = await this.generateEmbedding(query);

      // 2. Buscar chunks similares
      const { data: results, error } = await this.supabase.rpc(
        'search_similar_chunks',
        {
          query_embedding: queryEmbedding,
          match_threshold: threshold,
          match_count: limit
        }
      );

      if (error) throw error;

      return results || [];

    } catch (error) {
      console.error('❌ Erro na busca semântica:', error);
      throw error;
    }
  }

  /**
   * Buscar em documento específico
   */
  async searchInSpecificDocument(query, documentName, options = {}) {
    const { limit = 5, threshold = 0.7 } = options;

    try {
      console.log(`🎯 Buscando especificamente no documento: ${documentName}`);

      // 1. Encontrar o documento pelo nome
      const { data: documents, error: docError } = await this.supabase
        .from('documents')
        .select('id, title')
        .ilike('title', `%${documentName}%`)
        .limit(1);

      if (docError) throw docError;

      if (!documents || documents.length === 0) {
        console.log(`⚠️ Documento não encontrado: ${documentName}`);
        return [];
      }

      const document = documents[0];
      console.log(`✅ Documento encontrado: ${document.title} (ID: ${document.id})`);

      // 2. Buscar chunks apenas desse documento
      const { data: chunks, error: chunkError } = await this.supabase
        .from('document_chunks')
        .select('*')
        .eq('document_id', document.id)
        .order('chunk_index', { ascending: true })
        .limit(limit);

      if (chunkError) throw chunkError;

      console.log(`📊 Encontrados ${chunks?.length || 0} chunks do documento específico`);

      return chunks || [];

    } catch (error) {
      console.error('❌ Erro na busca por documento específico:', error);
      return [];
    }
  }

  // Implementar outros métodos auxiliares...
}