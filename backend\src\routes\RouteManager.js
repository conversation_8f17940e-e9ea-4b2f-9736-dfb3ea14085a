import { Router } from 'express';

// Importar middlewares de autenticação
import { authenticateApiKey } from '../middleware/auth.js';

// Importar rotas existentes
import whatsappRoutes from './whatsapp.js';
import sessionRoutes from './session.js';
import webhookRoutes from './webhook.js';
import healthRoutes from './health.js';
import securityRoutes from './security.js';
import antiBanRoutes from './antiban.js';
import ragRouter from './rag.js';
import documentsRoutes from './documents.js';
import analyticsRoutes from './analytics.js';

/**
 * Gerenciador Centralizado de Rotas
 * Organiza e configura todas as rotas da API
 */
export class RouteManager {
  constructor(serviceManager, logger) {
    this.serviceManager = serviceManager;
    this.logger = logger;
    this.routes = new Map();
  }

  /**
   * Configurar todas as rotas
   */
  setupRoutes(app) {
    this.logger.info('🛣️ Configurando rotas da API...');

    // Middleware para disponibilizar serviços nas rotas
    app.use('/api', (req, res, next) => {
      req.serviceManager = this.serviceManager;
      req.whatsappService = this.serviceManager.getService('whatsapp');
      req.ragService = this.serviceManager.getService('rag');
      req.messageHandler = this.serviceManager.getService('messageHandler');
      req.sessionManager = this.serviceManager.getService('session');
      req.persistenceService = this.serviceManager.getService('persistence');
      req.antiBanService = this.serviceManager.getService('antiban');
      req.rafaelaService = this.serviceManager.getService('rafaelaResponse');
      next();
    });

    // Configurar rotas públicas (sem autenticação)
    this.setupPublicRoutes(app);

    // Configurar rotas protegidas
    this.setupProtectedRoutes(app);

    // Configurar rotas administrativas
    this.setupAdminRoutes(app);

    this.logger.info('✅ Rotas configuradas com sucesso');
  }

  /**
   * Configurar rotas públicas
   */
  setupPublicRoutes(app) {
    // Health check - sempre público
    app.use('/api/health', healthRoutes);
    this.routes.set('health', { path: '/api/health', protected: false });

    // Webhook - público para receber dados externos
    app.use('/api/webhook', webhookRoutes);
    this.routes.set('webhook', { path: '/api/webhook', protected: false });

    // RAG - público para frontend
    app.use('/api/rag', ragRouter);
    this.routes.set('rag', { path: '/api/rag', protected: false });

    // Documents - público para frontend
    app.use('/api/documents', documentsRoutes);
    this.routes.set('documents', { path: '/api/documents', protected: false });

    // Analytics - público para frontend
    app.use('/api/analytics', analyticsRoutes);
    this.routes.set('analytics', { path: '/api/analytics', protected: false });

    this.logger.info('📖 Rotas públicas configuradas');
  }

  /**
   * Configurar rotas protegidas (requerem autenticação básica)
   */
  setupProtectedRoutes(app) {
    // WhatsApp - protegido em produção, público em desenvolvimento
    if (process.env.NODE_ENV === 'production') {
      app.use('/api/whatsapp', authenticateApiKey(['whatsapp:read', 'whatsapp:write']), whatsappRoutes);
      this.routes.set('whatsapp', { path: '/api/whatsapp', protected: true, roles: ['whatsapp:read', 'whatsapp:write'] });
    } else {
      app.use('/api/whatsapp', whatsappRoutes);
      this.routes.set('whatsapp', { path: '/api/whatsapp', protected: false });
    }

    // Session - protegido em produção, público em desenvolvimento
    if (process.env.NODE_ENV === 'production') {
      app.use('/api/session', authenticateApiKey(['session:read', 'session:write']), sessionRoutes);
      this.routes.set('session', { path: '/api/session', protected: true, roles: ['session:read', 'session:write'] });
    } else {
      app.use('/api/session', sessionRoutes);
      this.routes.set('session', { path: '/api/session', protected: false });
    }

    this.logger.info('🔒 Rotas protegidas configuradas');
  }

  /**
   * Configurar rotas administrativas (sempre protegidas)
   */
  setupAdminRoutes(app) {
    // Security - sempre protegido
    app.use('/api/security', authenticateApiKey(['admin']), securityRoutes);
    this.routes.set('security', { path: '/api/security', protected: true, roles: ['admin'] });

    // Anti-ban - protegido
    app.use('/api/antiban', authenticateApiKey(['whatsapp:read', 'admin']), antiBanRoutes);
    this.routes.set('antiban', { path: '/api/antiban', protected: true, roles: ['whatsapp:read', 'admin'] });

    // Rota de gerenciamento de serviços
    app.use('/api/services', authenticateApiKey(['admin']), this.createServicesRouter());
    this.routes.set('services', { path: '/api/services', protected: true, roles: ['admin'] });

    this.logger.info('👑 Rotas administrativas configuradas');
  }

  /**
   * Criar router para gerenciamento de serviços
   */
  createServicesRouter() {
    const router = Router();

    // Status de todos os serviços
    router.get('/status', (req, res) => {
      try {
        const status = req.serviceManager.getServicesStatus();
        res.json({
          success: true,
          data: status,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: error.message
        });
      }
    });

    // Health check de todos os serviços
    router.get('/health', async (req, res) => {
      try {
        const health = await req.serviceManager.healthCheck();
        const allHealthy = Object.values(health).every(h => h === true);
        
        res.status(allHealthy ? 200 : 503).json({
          success: allHealthy,
          data: health,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: error.message
        });
      }
    });

    // Reiniciar serviço específico
    router.post('/:serviceName/restart', async (req, res) => {
      try {
        const { serviceName } = req.params;
        
        if (!req.serviceManager.services.has(serviceName)) {
          return res.status(404).json({
            success: false,
            error: `Serviço '${serviceName}' não encontrado`
          });
        }

        await req.serviceManager.restartService(serviceName);
        
        res.json({
          success: true,
          message: `Serviço '${serviceName}' reiniciado com sucesso`,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: error.message
        });
      }
    });

    // Parar serviço específico
    router.post('/:serviceName/stop', async (req, res) => {
      try {
        const { serviceName } = req.params;
        
        if (!req.serviceManager.services.has(serviceName)) {
          return res.status(404).json({
            success: false,
            error: `Serviço '${serviceName}' não encontrado`
          });
        }

        await req.serviceManager.stopService(serviceName);
        
        res.json({
          success: true,
          message: `Serviço '${serviceName}' parado com sucesso`,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: error.message
        });
      }
    });

    // Iniciar serviço específico
    router.post('/:serviceName/start', async (req, res) => {
      try {
        const { serviceName } = req.params;
        
        await req.serviceManager.initializeService(serviceName);
        
        res.json({
          success: true,
          message: `Serviço '${serviceName}' iniciado com sucesso`,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: error.message
        });
      }
    });

    return router;
  }

  /**
   * Obter informações sobre todas as rotas
   */
  getRoutesInfo() {
    const routesInfo = [];
    
    for (const [name, info] of this.routes) {
      routesInfo.push({
        name,
        path: info.path,
        protected: info.protected,
        roles: info.roles || [],
        available: true
      });
    }
    
    return routesInfo;
  }

  /**
   * Middleware para log de rotas acessadas
   */
  createRouteLogger() {
    return (req, res, next) => {
      const start = Date.now();
      
      res.on('finish', () => {
        const duration = Date.now() - start;
        const route = req.route?.path || req.path;
        
        this.logger.info(`${req.method} ${route} - ${res.statusCode} (${duration}ms)`);
      });
      
      next();
    };
  }

  /**
   * Middleware para validação de serviços necessários
   */
  createServiceValidator(requiredServices = []) {
    return (req, res, next) => {
      const unavailableServices = [];
      
      for (const serviceName of requiredServices) {
        if (!req.serviceManager.isServiceAvailable(serviceName)) {
          unavailableServices.push(serviceName);
        }
      }
      
      if (unavailableServices.length > 0) {
        return res.status(503).json({
          success: false,
          error: 'Serviços necessários não estão disponíveis',
          unavailableServices,
          code: 'SERVICES_UNAVAILABLE'
        });
      }
      
      next();
    };
  }

  /**
   * Criar rota de documentação da API
   */
  createApiDocsRoute() {
    return (req, res) => {
      const routes = this.getRoutesInfo();
      const serviceStatus = req.serviceManager.getServicesStatus();
      
      res.json({
        api: {
          name: 'Vereadora Rafaela Backend API',
          version: '2.0.0',
          description: 'API centralizada para o sistema WhatsApp da Vereadora Rafaela de Nilda'
        },
        routes,
        services: serviceStatus,
        timestamp: new Date().toISOString()
      });
    };
  }
}
