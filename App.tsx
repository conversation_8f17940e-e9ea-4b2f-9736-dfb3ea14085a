import { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { ChatInterface } from './components/ChatInterface';
import { DocumentInput } from './components/DocumentInput';
import { VereadoraHeader } from './components/VereadoraHeader';
import { Sidebar } from './components/Sidebar';
import { whatsappService } from './services/whatsappService';
import { DocumentManager } from './components/DocumentManager';
import { MonitoringDashboard } from './components/MonitoringDashboard';
import { vereadoraRAGService } from './services/vereadoraRAGService';
import { WhatsAppManager } from './components/WhatsAppManager';
import { WhatsAppChats } from './components/WhatsAppChats';
import { databaseService } from './services/databaseService';
import { monitoringService } from './services/monitoringService';
import { isSupabaseConfigured } from './config/supabase';
import { isGeminiConfigured } from './config/gemini';
import { AppStateProvider, useAppState } from './contexts/AppStateContext';
import type { Message, Document } from './types';

// Constantes para melhor manutenção
const TABS = [
  { id: 'chat', icon: '💬', name: 'Chat' },
  { id: 'documents', icon: '📄', name: 'Docs' },
  { id: 'monitoring', icon: '📊', name: 'Monitor' },
  { id: 'whatsapp', icon: '📱', name: 'WhatsApp' }
] as const;

type TabType = typeof TABS[number]['id'];

// Componente interno que usa o contexto
function AppContent() {
  const { state, actions } = useAppState();
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<TabType>('chat');

  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Memoização para evitar recálculos desnecessários
  const isDemoMode = useMemo(() => !isSupabaseConfigured || !isGeminiConfigured, []);

  // Função otimizada para scroll
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  // Effect para scroll automático
  useEffect(() => {
    if (messages.length > 0) {
      scrollToBottom();
    }
  }, [messages, scrollToBottom]);

  // Função para gerar ID único mais robusta
  const generateId = useCallback(() => {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  // Função para adicionar mensagem de forma mais limpa
  const addMessage = useCallback((message: Omit<Message, 'id'>) => {
    const newMessage: Message = {
      ...message,
      id: generateId(),
    };
    setMessages(prev => [...prev, newMessage]);
    return newMessage;
  }, [generateId]);

  // Função para registrar métricas com tratamento de erro
  const recordMetrics = useCallback(async (type: 'message_sent' | 'message_received' | 'document_upload' | 'error', data: any) => {
    try {
      await monitoringService.recordInteraction(type, {
        ...data,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.warn('Erro ao registrar métricas:', error);
    }
  }, []);

  const handleSendMessage = useCallback(async (content: string) => {
    if (!content.trim()) return;

    // Adicionar mensagem do usuário
    addMessage({
      content,
      sender: 'user',
      timestamp: new Date(),
    });

    setIsLoading(true);

    try {
      // Registrar métricas
      await recordMetrics('message_sent', {
        messageLength: content.length
      });

      const response = await vereadoraRAGService.processMessage(content);
      
      // Adicionar resposta do assistente
      addMessage({
        content: response.answer,
        sender: 'assistant',
        timestamp: new Date(),
        sources: response.sources,
        confidence: response.confidence
      });

      // Registrar métricas de resposta
      await recordMetrics('message_received', {
        responseLength: response.answer.length,
        confidence: response.confidence,
        sourcesCount: response.sources?.length || 0
      });

    } catch (error) {
      console.error('Erro ao processar mensagem:', error);
      
      // Adicionar mensagem de erro
      addMessage({
        content: 'Desculpe, ocorreu um erro ao processar sua mensagem. Tente novamente.',
        sender: 'assistant',
        timestamp: new Date(),
      });

      // Registrar erro
      await recordMetrics('error', {
        error: error instanceof Error ? error.message : 'Erro desconhecido',
        type: 'message_processing'
      });
    } finally {
      setIsLoading(false);
    }
  }, [addMessage, recordMetrics]);

  const handleDocumentUpload = useCallback(async (file: File) => {
    console.log('📄 App: handleDocumentUpload chamado para:', file.name);
    
    if (!file) {
      console.error('Arquivo não fornecido');
      return;
    }

    try {
      setIsLoading(true);

      await recordMetrics('document_upload', {
        fileName: file.name,
        fileSize: file.size,
        status: 'start'
      });

      console.log('📄 App: Chamando vereadoraRAGService.uploadDocument...');
      const document = await vereadoraRAGService.uploadDocument(file);
      console.log('📄 App: Upload concluído:', document);

      console.log('📄 App: Recarregando lista de documentos...');
      await actions.updateDocuments();

      await recordMetrics('document_upload', {
        documentId: document.id,
        fileName: file.name,
        status: 'success'
      });

      // Adicionar mensagem de confirmação
      addMessage({
        content: `✅ Documento "${file.name}" foi carregado com sucesso! Agora posso responder perguntas sobre seu conteúdo.`,
        sender: 'assistant',
        timestamp: new Date(),
      });

      console.log('📄 App: Mensagem de confirmação adicionada');

    } catch (error) {
      console.error('❌ App: Erro ao fazer upload:', error);

      await recordMetrics('error', {
        fileName: file.name,
        type: 'document_upload',
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });

      // Adicionar mensagem de erro
      addMessage({
        content: `❌ Erro ao carregar o documento "${file.name}". Tente novamente.`,
        sender: 'assistant',
        timestamp: new Date(),
      });

      console.log('📄 App: Mensagem de erro adicionada');
    } finally {
      setIsLoading(false);
    }
  }, [actions, addMessage, recordMetrics]);

  const handleDeleteDocument = useCallback(async (documentId: string) => {
    try {
      await databaseService.deleteDocument(documentId);
      await actions.updateDocuments();
      
      // Registrar métricas de exclusão
      await recordMetrics('document_upload', {
        documentId,
        action: 'deleted'
      });
    } catch (error) {
      console.error('Erro ao deletar documento:', error);
      
      await recordMetrics('error', {
        type: 'document_deletion',
        documentId,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  }, [actions, recordMetrics]);

  // Componente para renderizar conteúdo da aba
  const renderTabContent = useCallback(() => {
    switch (activeTab) {
      case 'chat':
        return (
          <div className="flex flex-col h-full">
            <div className="flex-1 overflow-hidden">
              <ChatInterface
                messages={messages}
                onSendMessage={handleSendMessage}
                isLoading={isLoading}
                messagesEndRef={messagesEndRef}
              />
            </div>
          </div>
        );

      case 'documents':
        return (
          <DocumentManager
            documents={state.documents.documents}
            onDeleteDocument={handleDeleteDocument}
            onUpload={handleDocumentUpload}
          />
        );

      case 'monitoring':
        return <MonitoringDashboard />;
        
      case 'whatsapp':
        return (
          <div className="space-y-6">
            <WhatsAppManager />
            <WhatsAppChats isConnected={state.whatsapp.isConnected} />
          </div>
        );
        
      default:
        return (
          <div className="flex items-center justify-center h-full">
            <p className="text-gray-500">Aba não encontrada</p>
          </div>
        );
    }
  }, [activeTab, messages, handleSendMessage, isLoading, state.documents.documents, 
      state.whatsapp.isConnected, handleDeleteDocument, handleDocumentUpload]);

  // Componente para o banner de modo demo
  const DemoModeBanner = useMemo(() => {
    if (!isDemoMode) return null;

    return (
      <div className="max-w-6xl mx-auto px-6 py-4">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-6 w-6 text-yellow-600" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-4">
              <h3 className="text-base font-medium text-yellow-800">
                Modo Demonstração Ativo
              </h3>
              <div className="mt-2 text-base text-yellow-700">
                <p className="mb-3">
                  O sistema está funcionando em modo demonstração com todas as funcionalidades ativas!
                </p>
                <div className="space-y-2">
                  {!isSupabaseConfigured && (
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-yellow-600 rounded-full"></div>
                      <span>Configure Supabase para persistência de dados</span>
                    </div>
                  )}
                  {!isGeminiConfigured && (
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-yellow-600 rounded-full"></div>
                      <span>Configure Gemini para IA avançada</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }, [isDemoMode]);

  // Componente para navegação mobile
  const MobileNavigation = useMemo(() => (
    <div className="lg:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-50">
      <div className="flex justify-around py-2">
        {TABS.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex flex-col items-center py-2 px-3 rounded-lg transition-all ${
              activeTab === tab.id
                ? 'text-blue-600 bg-blue-50'
                : 'text-gray-600 hover:text-gray-800'
            }`}
            aria-label={`Ir para ${tab.name}`}
          >
            <span className="text-lg mb-1">{tab.icon}</span>
            <span className="text-xs font-medium">{tab.name}</span>
          </button>
        ))}
      </div>
    </div>
  ), [activeTab]);

  return (
    <div className="min-h-screen bg-gray-50">
      <VereadoraHeader whatsappConnected={state.whatsapp.isConnected} />

      {DemoModeBanner}

      {/* Layout principal com sidebar à esquerda */}
      <div className="flex h-[calc(100vh-120px)] relative">
        {/* Sidebar à esquerda - responsiva */}
        <div className="hidden lg:block">
          <Sidebar activeTab={activeTab} onTabChange={setActiveTab} />
        </div>

        {/* Conteúdo principal expandido */}
        <div className="flex-1 ml-0 lg:ml-64 transition-all duration-300 px-6">
          <div className="h-full bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            {renderTabContent()}
          </div>
        </div>

        {/* Sidebar mobile - bottom navigation */}
        {MobileNavigation}
      </div>
    </div>
  );
}

// Componente principal com contexto
function App() {
  return (
    <AppStateProvider>
      <AppContent />
    </AppStateProvider>
  );
}

export default App;