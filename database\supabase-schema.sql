-- =====================================================
-- SCHEMA SUPABASE - VEREADORA RAFAELA RAG SYSTEM
-- =====================================================
-- Sistema completo para RAG, WhatsApp e Analytics
-- Criado para: Backend WhatsApp - Vereadora Rafaela de Nilda
-- =====================================================

-- Habilitar extensões necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "vector";

-- =====================================================
-- 1. TABELA DE DOCUMENTOS
-- =====================================================
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path TEXT,
    file_size BIGINT,
    mime_type VARCHAR(100),
    status VARCHAR(50) DEFAULT 'processing' CHECK (status IN ('processing', 'ready', 'error', 'deleted')),
    upload_date TIMESTAMPTZ DEFAULT NOW(),
    processed_date TIMESTAMPTZ,
    extracted_text TEXT,
    chunks_count INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Índices para documentos
CREATE INDEX idx_documents_status ON documents(status);
CREATE INDEX idx_documents_upload_date ON documents(upload_date);
CREATE INDEX idx_documents_name ON documents(name);

-- =====================================================
-- 2. TABELA DE CHUNKS (FRAGMENTOS DE DOCUMENTOS)
-- =====================================================
CREATE TABLE document_chunks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    chunk_index INTEGER NOT NULL,
    content TEXT NOT NULL,
    content_hash VARCHAR(64),
    word_count INTEGER,
    page_number INTEGER,
    embedding vector(1536), -- Para embeddings OpenAI/Gemini
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Índices para chunks
CREATE INDEX idx_chunks_document_id ON document_chunks(document_id);
CREATE INDEX idx_chunks_chunk_index ON document_chunks(chunk_index);
CREATE INDEX idx_chunks_content_hash ON document_chunks(content_hash);
-- Índice para busca por similaridade de vetores
CREATE INDEX idx_chunks_embedding ON document_chunks USING ivfflat (embedding vector_cosine_ops);

-- =====================================================
-- 3. TABELA DE SESSÕES WHATSAPP
-- =====================================================
CREATE TABLE whatsapp_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_name VARCHAR(100) NOT NULL UNIQUE,
    phone_number VARCHAR(20),
    status VARCHAR(50) DEFAULT 'disconnected' CHECK (status IN ('connected', 'disconnected', 'connecting', 'error')),
    qr_code TEXT,
    last_activity TIMESTAMPTZ,
    connection_data JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Índices para sessões WhatsApp
CREATE INDEX idx_whatsapp_sessions_status ON whatsapp_sessions(status);
CREATE INDEX idx_whatsapp_sessions_phone ON whatsapp_sessions(phone_number);

-- =====================================================
-- 4. TABELA DE CONVERSAS WHATSAPP
-- =====================================================
CREATE TABLE whatsapp_conversations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID NOT NULL REFERENCES whatsapp_sessions(id) ON DELETE CASCADE,
    chat_id VARCHAR(100) NOT NULL,
    contact_name VARCHAR(255),
    phone_number VARCHAR(20),
    is_group BOOLEAN DEFAULT FALSE,
    last_message_date TIMESTAMPTZ,
    message_count INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(session_id, chat_id)
);

-- Índices para conversas
CREATE INDEX idx_conversations_session_id ON whatsapp_conversations(session_id);
CREATE INDEX idx_conversations_chat_id ON whatsapp_conversations(chat_id);
CREATE INDEX idx_conversations_phone ON whatsapp_conversations(phone_number);

-- =====================================================
-- 5. TABELA DE MENSAGENS WHATSAPP
-- =====================================================
CREATE TABLE whatsapp_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID NOT NULL REFERENCES whatsapp_conversations(id) ON DELETE CASCADE,
    message_id VARCHAR(100) NOT NULL,
    from_number VARCHAR(20) NOT NULL,
    to_number VARCHAR(20) NOT NULL,
    message_type VARCHAR(50) DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'audio', 'video', 'document', 'location', 'contact')),
    content TEXT,
    media_url TEXT,
    is_from_me BOOLEAN DEFAULT FALSE,
    timestamp TIMESTAMPTZ NOT NULL,
    status VARCHAR(50) DEFAULT 'received' CHECK (status IN ('sent', 'delivered', 'read', 'received', 'error')),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(conversation_id, message_id)
);

-- Índices para mensagens
CREATE INDEX idx_messages_conversation_id ON whatsapp_messages(conversation_id);
CREATE INDEX idx_messages_timestamp ON whatsapp_messages(timestamp);
CREATE INDEX idx_messages_from_number ON whatsapp_messages(from_number);
CREATE INDEX idx_messages_type ON whatsapp_messages(message_type);

-- =====================================================
-- 6. TABELA DE INTERAÇÕES RAG
-- =====================================================
CREATE TABLE rag_interactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id VARCHAR(100),
    user_message TEXT NOT NULL,
    ai_response TEXT NOT NULL,
    confidence_score DECIMAL(3,2),
    processing_time_ms INTEGER,
    chunks_used INTEGER DEFAULT 0,
    sources_count INTEGER DEFAULT 0,
    interaction_type VARCHAR(50) DEFAULT 'chat' CHECK (interaction_type IN ('chat', 'whatsapp', 'api')),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Índices para interações RAG
CREATE INDEX idx_rag_interactions_session ON rag_interactions(session_id);
CREATE INDEX idx_rag_interactions_type ON rag_interactions(interaction_type);
CREATE INDEX idx_rag_interactions_created_at ON rag_interactions(created_at);

-- =====================================================
-- 7. TABELA DE ANALYTICS E LOGS
-- =====================================================
CREATE TABLE analytics_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type VARCHAR(100) NOT NULL,
    event_data JSONB DEFAULT '{}',
    user_session VARCHAR(100),
    ip_address INET,
    user_agent TEXT,
    timestamp TIMESTAMPTZ DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'
);

-- Índices para analytics
CREATE INDEX idx_analytics_event_type ON analytics_logs(event_type);
CREATE INDEX idx_analytics_timestamp ON analytics_logs(timestamp);
CREATE INDEX idx_analytics_session ON analytics_logs(user_session);

-- =====================================================
-- 8. TABELA DE CONFIGURAÇÕES DO SISTEMA
-- =====================================================
CREATE TABLE system_config (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value JSONB NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Índice para configurações
CREATE INDEX idx_system_config_key ON system_config(config_key);
CREATE INDEX idx_system_config_active ON system_config(is_active);

-- =====================================================
-- 9. TRIGGERS PARA UPDATED_AT
-- =====================================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Aplicar triggers
CREATE TRIGGER update_documents_updated_at BEFORE UPDATE ON documents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_chunks_updated_at BEFORE UPDATE ON document_chunks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_sessions_updated_at BEFORE UPDATE ON whatsapp_sessions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_conversations_updated_at BEFORE UPDATE ON whatsapp_conversations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_config_updated_at BEFORE UPDATE ON system_config FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 10. POLÍTICAS DE SEGURANÇA (RLS)
-- =====================================================

-- Habilitar RLS nas tabelas principais
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_chunks ENABLE ROW LEVEL SECURITY;
ALTER TABLE whatsapp_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE whatsapp_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE whatsapp_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE rag_interactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_config ENABLE ROW LEVEL SECURITY;

-- Políticas para documentos (acesso público para leitura, autenticado para escrita)
CREATE POLICY "Documentos - Leitura pública" ON documents FOR SELECT USING (true);
CREATE POLICY "Documentos - Escrita autenticada" ON documents FOR INSERT WITH CHECK (true);
CREATE POLICY "Documentos - Atualização autenticada" ON documents FOR UPDATE USING (true);
CREATE POLICY "Documentos - Exclusão autenticada" ON documents FOR DELETE USING (true);

-- Políticas para chunks (seguem as mesmas regras dos documentos)
CREATE POLICY "Chunks - Leitura pública" ON document_chunks FOR SELECT USING (true);
CREATE POLICY "Chunks - Escrita autenticada" ON document_chunks FOR INSERT WITH CHECK (true);
CREATE POLICY "Chunks - Atualização autenticada" ON document_chunks FOR UPDATE USING (true);
CREATE POLICY "Chunks - Exclusão autenticada" ON document_chunks FOR DELETE USING (true);

-- Políticas para sessões WhatsApp (acesso restrito)
CREATE POLICY "WhatsApp Sessions - Acesso total" ON whatsapp_sessions FOR ALL USING (true);

-- Políticas para conversas WhatsApp
CREATE POLICY "WhatsApp Conversations - Acesso total" ON whatsapp_conversations FOR ALL USING (true);

-- Políticas para mensagens WhatsApp
CREATE POLICY "WhatsApp Messages - Acesso total" ON whatsapp_messages FOR ALL USING (true);

-- Políticas para interações RAG
CREATE POLICY "RAG Interactions - Leitura pública" ON rag_interactions FOR SELECT USING (true);
CREATE POLICY "RAG Interactions - Escrita autenticada" ON rag_interactions FOR INSERT WITH CHECK (true);

-- Políticas para analytics
CREATE POLICY "Analytics - Leitura pública" ON analytics_logs FOR SELECT USING (true);
CREATE POLICY "Analytics - Escrita autenticada" ON analytics_logs FOR INSERT WITH CHECK (true);

-- Políticas para configurações do sistema
CREATE POLICY "System Config - Leitura pública" ON system_config FOR SELECT USING (true);
CREATE POLICY "System Config - Escrita autenticada" ON system_config FOR INSERT WITH CHECK (true);
CREATE POLICY "System Config - Atualização autenticada" ON system_config FOR UPDATE USING (true);

-- =====================================================
-- 11. FUNÇÕES ÚTEIS
-- =====================================================

-- Função para busca semântica de chunks
CREATE OR REPLACE FUNCTION search_similar_chunks(
    query_embedding vector(1536),
    match_threshold float DEFAULT 0.7,
    match_count int DEFAULT 5
)
RETURNS TABLE (
    id uuid,
    document_id uuid,
    content text,
    similarity float,
    metadata jsonb
)
LANGUAGE sql
AS $$
    SELECT
        dc.id,
        dc.document_id,
        dc.content,
        1 - (dc.embedding <=> query_embedding) as similarity,
        dc.metadata
    FROM document_chunks dc
    WHERE dc.embedding IS NOT NULL
        AND 1 - (dc.embedding <=> query_embedding) > match_threshold
    ORDER BY dc.embedding <=> query_embedding
    LIMIT match_count;
$$;

-- Função para obter estatísticas de documentos
CREATE OR REPLACE FUNCTION get_document_stats()
RETURNS TABLE (
    total_documents bigint,
    total_chunks bigint,
    total_size bigint,
    documents_by_status jsonb
)
LANGUAGE sql
AS $$
    SELECT
        COUNT(*) as total_documents,
        COALESCE(SUM(chunks_count), 0) as total_chunks,
        COALESCE(SUM(file_size), 0) as total_size,
        jsonb_object_agg(status, count) as documents_by_status
    FROM (
        SELECT
            status,
            COUNT(*) as count,
            chunks_count,
            file_size
        FROM documents
        GROUP BY status, chunks_count, file_size
    ) stats;
$$;

-- Função para obter estatísticas do WhatsApp
CREATE OR REPLACE FUNCTION get_whatsapp_stats()
RETURNS TABLE (
    total_sessions bigint,
    active_sessions bigint,
    total_conversations bigint,
    total_messages bigint,
    messages_today bigint
)
LANGUAGE sql
AS $$
    SELECT
        (SELECT COUNT(*) FROM whatsapp_sessions) as total_sessions,
        (SELECT COUNT(*) FROM whatsapp_sessions WHERE status = 'connected') as active_sessions,
        (SELECT COUNT(*) FROM whatsapp_conversations) as total_conversations,
        (SELECT COUNT(*) FROM whatsapp_messages) as total_messages,
        (SELECT COUNT(*) FROM whatsapp_messages WHERE DATE(created_at) = CURRENT_DATE) as messages_today;
$$;

-- =====================================================
-- 12. VIEWS ÚTEIS
-- =====================================================

-- View para documentos com estatísticas
CREATE VIEW documents_with_stats AS
SELECT
    d.*,
    COUNT(dc.id) as actual_chunks_count,
    AVG(dc.word_count) as avg_chunk_words,
    MAX(dc.created_at) as last_chunk_created
FROM documents d
LEFT JOIN document_chunks dc ON d.id = dc.document_id
GROUP BY d.id;

-- View para conversas com última mensagem
CREATE VIEW conversations_with_last_message AS
SELECT
    c.*,
    m.content as last_message_content,
    m.timestamp as last_message_timestamp,
    m.is_from_me as last_message_from_me
FROM whatsapp_conversations c
LEFT JOIN whatsapp_messages m ON c.id = m.conversation_id
WHERE m.timestamp = (
    SELECT MAX(timestamp)
    FROM whatsapp_messages m2
    WHERE m2.conversation_id = c.id
);

-- View para analytics diários
CREATE VIEW daily_analytics AS
SELECT
    DATE(timestamp) as date,
    event_type,
    COUNT(*) as event_count,
    COUNT(DISTINCT user_session) as unique_sessions
FROM analytics_logs
GROUP BY DATE(timestamp), event_type
ORDER BY date DESC, event_count DESC;

-- =====================================================
-- 13. DADOS INICIAIS
-- =====================================================

-- Configurações padrão do sistema
INSERT INTO system_config (config_key, config_value, description) VALUES
('gemini_api_key', '""', 'Chave da API do Google Gemini'),
('supabase_url', '""', 'URL do projeto Supabase'),
('supabase_anon_key', '""', 'Chave anônima do Supabase'),
('whatsapp_session_name', '"vereadora-rafaela"', 'Nome da sessão WhatsApp padrão'),
('rag_chunk_size', '1000', 'Tamanho padrão dos chunks em caracteres'),
('rag_chunk_overlap', '200', 'Sobreposição entre chunks em caracteres'),
('max_search_results', '5', 'Número máximo de resultados na busca semântica'),
('confidence_threshold', '0.7', 'Limite mínimo de confiança para respostas'),
('system_status', '"active"', 'Status geral do sistema'),
('maintenance_mode', 'false', 'Modo de manutenção ativo/inativo')
ON CONFLICT (config_key) DO NOTHING;

-- =====================================================
-- 14. COMENTÁRIOS E DOCUMENTAÇÃO
-- =====================================================

COMMENT ON TABLE documents IS 'Armazena informações sobre documentos PDF enviados';
COMMENT ON TABLE document_chunks IS 'Fragmentos de texto extraídos dos documentos para busca semântica';
COMMENT ON TABLE whatsapp_sessions IS 'Sessões ativas do WhatsApp';
COMMENT ON TABLE whatsapp_conversations IS 'Conversas individuais ou em grupo do WhatsApp';
COMMENT ON TABLE whatsapp_messages IS 'Mensagens enviadas e recebidas via WhatsApp';
COMMENT ON TABLE rag_interactions IS 'Histórico de interações com o sistema RAG';
COMMENT ON TABLE analytics_logs IS 'Logs de eventos e analytics do sistema';
COMMENT ON TABLE system_config IS 'Configurações gerais do sistema';

-- =====================================================
-- SCHEMA CRIADO COM SUCESSO!
-- =====================================================
-- Para usar este schema:
-- 1. Copie todo o conteúdo deste arquivo
-- 2. Execute no SQL Editor do Supabase
-- 3. Configure as variáveis de ambiente no backend
-- 4. Teste a conexão
-- =====================================================
