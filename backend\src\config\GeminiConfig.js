/**
 * Configuração Avançada do Gemini AI
 * Otimizada para a Vereadora Rafaela de Nilda
 */

export class GeminiConfig {
  constructor() {
    this.apiKey = process.env.GEMINI_API_KEY;
    this.model = process.env.GEMINI_MODEL || 'gemini-1.5-flash';
    this.fallbackModel = 'gemini-1.5-flash';
    
    // Configurações de geração otimizadas
    this.generationConfig = {
      temperature: parseFloat(process.env.GEMINI_TEMPERATURE) || 0.8,
      topK: parseInt(process.env.GEMINI_TOP_K) || 40,
      topP: parseFloat(process.env.GEMINI_TOP_P) || 0.95,
      maxOutputTokens: parseInt(process.env.GEMINI_MAX_TOKENS) || 1024,
      responseMimeType: "text/plain",
    };
    
    // Configurações de segurança
    this.safetySettings = [
      {
        category: "HARM_CATEGORY_HARASSMENT",
        threshold: "BLOCK_MEDIUM_AND_ABOVE",
      },
      {
        category: "HARM_CATEGORY_HATE_SPEECH",
        threshold: "BLOCK_MEDIUM_AND_ABOVE",
      },
      {
        category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
        threshold: "BLOCK_MEDIUM_AND_ABOVE",
      },
      {
        category: "HARM_CATEGORY_DANGEROUS_CONTENT",
        threshold: "BLOCK_MEDIUM_AND_ABOVE",
      },
    ];
    
    // Configurações de cache
    this.cache = {
      enabled: process.env.GEMINI_CACHE_ENABLED !== 'false',
      maxSize: parseInt(process.env.GEMINI_CACHE_SIZE) || 100,
      ttl: parseInt(process.env.GEMINI_CACHE_TTL) || 3600000, // 1 hora
    };
    
    // Configurações de retry
    this.retry = {
      maxAttempts: parseInt(process.env.GEMINI_MAX_RETRIES) || 3,
      delay: parseInt(process.env.GEMINI_RETRY_DELAY) || 1000,
      backoff: parseFloat(process.env.GEMINI_RETRY_BACKOFF) || 2.0,
    };
    
    // Configurações de timeout
    this.timeout = {
      request: parseInt(process.env.GEMINI_REQUEST_TIMEOUT) || 30000,
      connection: parseInt(process.env.GEMINI_CONNECTION_TIMEOUT) || 10000,
    };
    
    // Configurações específicas da Vereadora Rafaela
    this.rafaela = {
      maxResponseLength: 1000, // Para WhatsApp
      preferredEmojis: ['🙏🏽', '💖', '🤩', '😍', '👏', '🏛️', '🌟'],
      responseStyle: 'calorosa_e_proxima',
      focusAreas: [
        'saude_publica',
        'educacao',
        'infraestrutura',
        'seguranca',
        'apoio_familias',
        'desenvolvimento_economico',
        'meio_ambiente'
      ]
    };
    
    // Prompts pré-definidos
    this.prompts = {
      base: this.getBasePrompt(),
      greeting: this.getGreetingPrompt(),
      complaint: this.getComplaintPrompt(),
      request: this.getRequestPrompt(),
      question: this.getQuestionPrompt(),
      emergency: this.getEmergencyPrompt()
    };
  }

  getBasePrompt() {
    return `Você é a Vereadora Rafaela de Nilda, eleita democraticamente para representar o povo de Parnamirim/RN no período 2025-2028.

IDENTIDADE POLÍTICA:
- Nome: Vereadora Rafaela de Nilda
- Município: Parnamirim/RN
- Mandato: 2025-2028
- Partido: SOLIDARIEDADE
- Cargo: Vereadora Municipal

PERSONALIDADE AUTÊNTICA:
- Carinhosa, atenciosa e genuinamente próxima do povo
- Linguagem acessível, calorosa e humana
- Sempre prestativa e solicita para ajudar
- Comprometida com a transparência e honestidade
- Focada no bem-estar da população de Parnamirim
- Defensora dos direitos sociais e da justiça

ESTILO DE COMUNICAÇÃO:
- Use emojis naturalmente: 🙏🏽💖🤩😍👏🏛️🌟
- Linguagem informal mas respeitosa
- Respostas concisas (máximo 2 parágrafos para WhatsApp)
- Tom caloroso e acolhedor
- Sempre ofereça ajuda concreta quando possível

ÁREAS DE ATUAÇÃO PRIORITÁRIAS:
- Saúde pública e bem-estar social
- Educação e oportunidades para jovens
- Infraestrutura urbana e mobilidade
- Segurança pública e iluminação
- Apoio a mulheres e famílias
- Desenvolvimento econômico local
- Meio ambiente e sustentabilidade`;
  }

  getGreetingPrompt() {
    return `CONTEXTO: O cidadão está cumprimentando você.

INSTRUÇÕES ESPECÍFICAS:
1. Responda à saudação de forma calorosa
2. Use o horário apropriado (bom dia/boa tarde/boa noite)
3. Demonstre alegria em falar com o cidadão
4. Pergunte como pode ajudar
5. Use emojis apropriados
6. Mantenha tom acolhedor e próximo`;
  }

  getComplaintPrompt() {
    return `CONTEXTO: O cidadão está fazendo uma reclamação ou relatando um problema.

INSTRUÇÕES ESPECÍFICAS:
1. Demonstre empatia e compreensão
2. Agradeça por trazer o problema à sua atenção
3. Ofereça encaminhamento concreto
4. Explique próximos passos quando possível
5. Peça informações adicionais se necessário
6. Mantenha tom solidário e proativo
7. Use emojis que demonstrem cuidado (🙏🏽💖)`;
  }

  getRequestPrompt() {
    return `CONTEXTO: O cidadão está fazendo uma solicitação ou pedindo ajuda.

INSTRUÇÕES ESPECÍFICAS:
1. Seja específica sobre como pode ajudar
2. Explique o processo ou próximos passos
3. Ofereça alternativas quando apropriado
4. Peça informações necessárias
5. Demonstre disposição para ajudar
6. Use tom prestativo e encorajador
7. Inclua emojis positivos (🤩👏)`;
  }

  getQuestionPrompt() {
    return `CONTEXTO: O cidadão está fazendo uma pergunta ou buscando informações.

INSTRUÇÕES ESPECÍFICAS:
1. Responda de forma educativa e clara
2. Ofereça informações adicionais relevantes
3. Seja honesta se não souber algo específico
4. Direcione para fontes oficiais quando apropriado
5. Mantenha linguagem acessível
6. Use tom informativo mas caloroso
7. Termine oferecendo ajuda adicional`;
  }

  getEmergencyPrompt() {
    return `CONTEXTO: A mensagem indica uma situação de emergência ou urgência.

INSTRUÇÕES ESPECÍFICAS:
1. Priorize orientações de segurança
2. Forneça contatos de emergência (192, 193, 190)
3. Ofereça encaminhamento imediato
4. Use tom sério mas acolhedor
5. Seja clara e objetiva
6. Demonstre preocupação genuína
7. Acompanhe com ações concretas`;
  }

  /**
   * Obter configuração completa para o modelo
   */
  getModelConfig() {
    return {
      model: this.model,
      generationConfig: this.generationConfig,
      safetySettings: this.safetySettings,
    };
  }

  /**
   * Obter prompt baseado no tipo de mensagem
   */
  getPromptForType(messageType) {
    return this.prompts[messageType] || this.prompts.base;
  }

  /**
   * Validar configuração
   */
  validate() {
    const errors = [];
    
    if (!this.apiKey) {
      errors.push('GEMINI_API_KEY não configurada');
    }
    
    if (this.generationConfig.temperature < 0 || this.generationConfig.temperature > 2) {
      errors.push('GEMINI_TEMPERATURE deve estar entre 0 e 2');
    }
    
    if (this.generationConfig.topK < 1 || this.generationConfig.topK > 100) {
      errors.push('GEMINI_TOP_K deve estar entre 1 e 100');
    }
    
    if (this.generationConfig.topP < 0 || this.generationConfig.topP > 1) {
      errors.push('GEMINI_TOP_P deve estar entre 0 e 1');
    }
    
    if (this.generationConfig.maxOutputTokens < 1 || this.generationConfig.maxOutputTokens > 8192) {
      errors.push('GEMINI_MAX_TOKENS deve estar entre 1 e 8192');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Obter resumo da configuração
   */
  getSummary() {
    return {
      model: this.model,
      fallbackModel: this.fallbackModel,
      hasApiKey: !!this.apiKey,
      temperature: this.generationConfig.temperature,
      maxTokens: this.generationConfig.maxOutputTokens,
      cacheEnabled: this.cache.enabled,
      cacheSize: this.cache.maxSize,
      maxRetries: this.retry.maxAttempts,
      requestTimeout: this.timeout.request,
      responseStyle: this.rafaela.responseStyle,
      focusAreas: this.rafaela.focusAreas.length
    };
  }

  /**
   * Atualizar configuração em tempo real
   */
  updateConfig(updates) {
    if (updates.temperature !== undefined) {
      this.generationConfig.temperature = Math.max(0, Math.min(2, updates.temperature));
    }
    
    if (updates.maxTokens !== undefined) {
      this.generationConfig.maxOutputTokens = Math.max(1, Math.min(8192, updates.maxTokens));
    }
    
    if (updates.cacheEnabled !== undefined) {
      this.cache.enabled = updates.cacheEnabled;
    }
    
    if (updates.maxRetries !== undefined) {
      this.retry.maxAttempts = Math.max(1, Math.min(10, updates.maxRetries));
    }
    
    return this.getSummary();
  }
}
