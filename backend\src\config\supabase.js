import { createClient } from '@supabase/supabase-js';
import { Logger } from '../utils/Logger.js';

/**
 * Configuração e cliente Supabase
 */
export class SupabaseConfig {
  constructor() {
    this.logger = new Logger('SupabaseConfig');
    this.client = null;
    this.isConnected = false;
  }

  /**
   * Inicializar conexão com Supabase
   */
  async initialize() {
    try {
      const supabaseUrl = process.env.SUPABASE_URL;
      const supabaseKey = process.env.SUPABASE_ANON_KEY;

      if (!supabaseUrl || !supabaseKey) {
        this.logger.warn('⚠️ Credenciais do Supabase não configuradas');
        return false;
      }

      this.client = createClient(supabaseUrl, supabaseKey, {
        auth: {
          persistSession: false
        },
        db: {
          schema: 'public'
        }
      });

      // Testar conexão
      const { data, error } = await this.client
        .from('system_config')
        .select('config_key')
        .limit(1);

      if (error) {
        this.logger.error('❌ Erro ao conectar com Supabase:', error);
        return false;
      }

      this.isConnected = true;
      this.logger.info('✅ Supabase conectado com sucesso');
      return true;

    } catch (error) {
      this.logger.error('❌ Erro ao inicializar Supabase:', error);
      return false;
    }
  }

  /**
   * Obter cliente Supabase
   */
  getClient() {
    if (!this.client) {
      throw new Error('Supabase não inicializado');
    }
    return this.client;
  }

  /**
   * Verificar se está conectado
   */
  isReady() {
    return this.isConnected && this.client !== null;
  }

  /**
   * Salvar documento
   */
  async saveDocument(document) {
    try {
      if (!this.isReady()) {
        throw new Error('Supabase não está conectado');
      }

      const { data, error } = await this.client
        .from('documents')
        .insert({
          id: document.id,
          name: document.name,
          original_name: document.original_name || document.name,
          file_path: document.path,
          file_size: document.size,
          mime_type: document.type,
          status: document.status,
          extracted_text: document.extracted_text,
          chunks_count: document.chunks_count,
          metadata: document.metadata || {}
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      this.logger.info(`✅ Documento salvo no Supabase: ${document.name}`);
      return data;

    } catch (error) {
      this.logger.error('❌ Erro ao salvar documento:', error);
      throw error;
    }
  }

  /**
   * Salvar chunks do documento
   */
  async saveDocumentChunks(documentId, chunks) {
    try {
      if (!this.isReady()) {
        throw new Error('Supabase não está conectado');
      }

      const chunksData = chunks.map((chunk, index) => ({
        id: chunk.id,
        document_id: documentId,
        chunk_index: index,
        content: chunk.content,
        content_hash: this.generateHash(chunk.content),
        word_count: chunk.content.split(' ').length,
        page_number: chunk.metadata?.page || Math.floor(index / 3) + 1,
        metadata: chunk.metadata || {}
      }));

      const { data, error } = await this.client
        .from('document_chunks')
        .insert(chunksData)
        .select();

      if (error) {
        throw error;
      }

      this.logger.info(`✅ ${chunks.length} chunks salvos no Supabase`);
      return data;

    } catch (error) {
      this.logger.error('❌ Erro ao salvar chunks:', error);
      throw error;
    }
  }

  /**
   * Buscar documentos
   */
  async getDocuments(limit = 50) {
    try {
      if (!this.isReady()) {
        return [];
      }

      const { data, error } = await this.client
        .from('documents')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        throw error;
      }

      return data || [];

    } catch (error) {
      this.logger.error('❌ Erro ao buscar documentos:', error);
      return [];
    }
  }

  /**
   * Buscar chunks de documentos
   */
  async getDocumentChunks(documentId = null, limit = 100) {
    try {
      if (!this.isReady()) {
        return [];
      }

      let query = this.client
        .from('document_chunks')
        .select('*')
        .order('chunk_index', { ascending: true })
        .limit(limit);

      if (documentId) {
        query = query.eq('document_id', documentId);
      }

      const { data, error } = await query;

      if (error) {
        throw error;
      }

      return data || [];

    } catch (error) {
      this.logger.error('❌ Erro ao buscar chunks:', error);
      return [];
    }
  }

  /**
   * Salvar interação RAG
   */
  async saveRAGInteraction(interaction) {
    try {
      if (!this.isReady()) {
        return null;
      }

      const { data, error } = await this.client
        .from('rag_interactions')
        .insert({
          session_id: interaction.session_id,
          user_message: interaction.user_message,
          ai_response: interaction.ai_response,
          confidence_score: interaction.confidence_score,
          processing_time_ms: interaction.processing_time_ms,
          chunks_used: interaction.chunks_used,
          sources_count: interaction.sources_count,
          interaction_type: interaction.interaction_type || 'chat',
          metadata: interaction.metadata || {}
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      return data;

    } catch (error) {
      this.logger.error('❌ Erro ao salvar interação RAG:', error);
      return null;
    }
  }

  /**
   * Salvar log de analytics
   */
  async saveAnalyticsLog(log) {
    try {
      if (!this.isReady()) {
        return null;
      }

      const { data, error } = await this.client
        .from('analytics_logs')
        .insert({
          event_type: log.event_type,
          event_data: log.event_data || {},
          user_session: log.user_session,
          ip_address: log.ip_address,
          user_agent: log.user_agent,
          metadata: log.metadata || {}
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      return data;

    } catch (error) {
      this.logger.error('❌ Erro ao salvar analytics:', error);
      return null;
    }
  }

  /**
   * Obter configuração do sistema
   */
  async getSystemConfig(key) {
    try {
      if (!this.isReady()) {
        return null;
      }

      const { data, error } = await this.client
        .from('system_config')
        .select('config_value')
        .eq('config_key', key)
        .eq('is_active', true)
        .single();

      if (error || !data) {
        return null;
      }

      return data.config_value;

    } catch (error) {
      this.logger.error(`❌ Erro ao buscar configuração ${key}:`, error);
      return null;
    }
  }

  /**
   * Gerar hash simples para conteúdo
   */
  generateHash(content) {
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash).toString(16);
  }
}

// Instância singleton
export const supabaseConfig = new SupabaseConfig();
