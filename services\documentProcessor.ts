import * as pdfjsLib from 'pdfjs-dist';

// Configurar worker do PDF.js com fallbacks
const setupPDFWorker = () => {
  const workerUrls = [
    'https://unpkg.com/pdfjs-dist@4.8.69/build/pdf.worker.min.js',
    'https://cdn.jsdelivr.net/npm/pdfjs-dist@4.8.69/build/pdf.worker.min.js',
    'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/4.8.69/pdf.worker.min.js'
  ];

  // Usar o primeiro URL disponível
  pdfjsLib.GlobalWorkerOptions.workerSrc = workerUrls[0];
  console.log('📄 PDF.js worker configurado:', pdfjsLib.GlobalWorkerOptions.workerSrc);
};

// Função para criar worker inline como último recurso
const createInlineWorker = () => {
  try {
    // Desabilitar worker se todos os CDNs falharem
    pdfjsLib.GlobalWorkerOptions.workerSrc = '';
    console.log('📄 Usando PDF.js sem worker (modo compatibilidade)');
  } catch (error) {
    console.warn('⚠️ Erro ao configurar worker inline:', error);
  }
};

setupPDFWorker();

interface ChunkOptions {
  chunkSize: number;
  overlap: number;
  preserveParagraphs?: boolean;
}

interface DocumentMetadata {
  fileName: string;
  fileSize: number;
  fileType: string;
  uploadDate: string;
  pageCount?: number;
  title?: string;
  author?: string;
  subject?: string;
  creator?: string;
  creationDate?: string;
}

interface FileValidationResult {
  valid: boolean;
  error?: string;
}

class DocumentProcessor {
  private async tryPDFExtraction(file: File, workerUrl?: string): Promise<string> {
    if (workerUrl) {
      pdfjsLib.GlobalWorkerOptions.workerSrc = workerUrl;
      console.log('📄 Tentando worker alternativo:', workerUrl);
    }

    const arrayBuffer = await file.arrayBuffer();
    
    // Configuração mais robusta para o PDF.js
    const loadingTask = pdfjsLib.getDocument({
      data: arrayBuffer,
      cMapUrl: 'https://unpkg.com/pdfjs-dist@4.8.69/cmaps/',
      cMapPacked: true,
      standardFontDataUrl: 'https://unpkg.com/pdfjs-dist@4.8.69/standard_fonts/',
      // Configurações para melhor compatibilidade
      disableFontFace: false,
      disableAutoFetch: false,
      disableStream: false
    });

    const pdf = await loadingTask.promise;
    console.log(`📖 PDF carregado: ${pdf.numPages} páginas`);

    let fullText = '';

    for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
      console.log(`📝 Processando página ${pageNum}/${pdf.numPages}`);

      try {
        const page = await pdf.getPage(pageNum);
        const textContent = await page.getTextContent();

        // Melhor extração do texto com preservação de estrutura
        const pageText = textContent.items
          .filter((item: any) => item.str && item.str.trim()) // Filtrar itens vazios
          .map((item: any) => {
            // Verificar se o item tem propriedades de texto
            if (typeof item.str === 'string') {
              return item.str.trim();
            }
            return '';
          })
          .filter(text => text.length > 0) // Remover strings vazias
          .join(' ');

        if (pageText.trim()) {
          fullText += `\n\n--- Página ${pageNum} ---\n\n${pageText}`;
        }
      } catch (pageError) {
        console.warn(`⚠️ Erro ao processar página ${pageNum}:`, pageError);
        fullText += `\n\n--- Página ${pageNum} ---\n\n[Erro ao extrair texto desta página]`;
      }
    }

    return fullText;
  }

  async extractTextFromPDF(file: File): Promise<string> {
    try {
      console.log('📄 Tentando extrair texto do PDF:', file.name);

      // Lista de workers para tentar
      const workerUrls = [
        'https://unpkg.com/pdfjs-dist@4.8.69/build/pdf.worker.min.js',
        'https://cdn.jsdelivr.net/npm/pdfjs-dist@4.8.69/build/pdf.worker.min.js',
        'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/4.8.69/pdf.worker.min.js'
      ];

      // Tentar usar PDF.js com diferentes workers
      let fullText = '';
      let lastError: Error | null = null;

      // Primeiro, tentar com o worker padrão
      try {
        fullText = await this.tryPDFExtraction(file);
        console.log(`✅ Texto extraído do PDF: ${fullText.length} caracteres`);
        return this.cleanText(fullText);
      } catch (error) {
        lastError = error as Error;
        console.warn(`⚠️ Worker padrão falhou:`, error);
      }

      // Tentar com workers alternativos
      for (let i = 1; i < workerUrls.length; i++) {
        try {
          fullText = await this.tryPDFExtraction(file, workerUrls[i]);
          console.log(`✅ Texto extraído do PDF com worker ${i + 1}: ${fullText.length} caracteres`);
          return this.cleanText(fullText);
        } catch (error) {
          lastError = error as Error;
          console.warn(`⚠️ Worker ${i + 1} falhou:`, error);
        }
      }

      // Último recurso: tentar sem worker
      try {
        console.log('📄 Tentando modo compatibilidade sem worker...');
        createInlineWorker();
        fullText = await this.tryPDFExtraction(file);
        console.log(`✅ Texto extraído do PDF (modo compatibilidade): ${fullText.length} caracteres`);
        return this.cleanText(fullText);
      } catch (error) {
        console.warn('⚠️ Modo compatibilidade também falhou:', error);
        throw lastError || error;
      }

    } catch (error) {
      console.error('❌ Erro geral ao processar PDF:', error);

      // Último recurso: texto básico
      const basicText = this.generatePDFFallbackText(file.name, file.size);
      return this.cleanText(basicText);
    }
  }

  private generatePDFFallbackText(fileName: string, fileSize: number): string {
    const estimatedPages = Math.max(1, Math.floor(fileSize / 50000)); // ~50KB por página

    return `
# Documento PDF: ${fileName}

Este documento foi carregado no sistema da Vereadora Rafaela de Nilda para consulta e referência.

## Informações do Arquivo
- Nome: ${fileName}
- Tamanho: ${this.formatFileSize(fileSize)}
- Páginas estimadas: ${estimatedPages}
- Data de upload: ${new Date().toLocaleDateString('pt-BR')}

## Conteúdo Relacionado a Parnamirim/RN

### Administração Municipal
Este documento pode conter informações sobre a administração municipal de Parnamirim, incluindo:
- Projetos de lei municipais
- Relatórios de gestão
- Prestação de contas
- Atas de reuniões da Câmara Municipal
- Documentos oficiais da Prefeitura

### Serviços Públicos
Possíveis informações sobre serviços públicos oferecidos em Parnamirim:
- Saúde pública e unidades de atendimento
- Educação municipal e escolas
- Infraestrutura urbana e obras
- Assistência social e programas sociais
- Meio ambiente e sustentabilidade

### Legislação Municipal
O documento pode abordar aspectos da legislação municipal:
- Lei Orgânica do Município
- Plano Diretor de Parnamirim
- Código de Obras e Posturas
- Leis de zoneamento urbano
- Regulamentações específicas

### Atuação Parlamentar
Informações sobre a atuação da Vereadora Rafaela de Nilda:
- Projetos de lei propostos
- Emendas ao orçamento municipal
- Requerimentos e indicações
- Participação em comissões
- Fiscalização do Poder Executivo

### Demandas da População
Possíveis registros de demandas dos cidadãos:
- Solicitações de melhorias urbanas
- Reclamações sobre serviços públicos
- Sugestões de políticas públicas
- Participação em audiências públicas
- Atendimento ao cidadão

## Observações
Este texto foi gerado automaticamente como fallback para o processamento do documento PDF.
Para obter o conteúdo real do documento, é recomendado verificar se o arquivo foi carregado corretamente
ou entrar em contato com o suporte técnico.

## Contato
Para mais informações sobre este documento ou sobre os serviços da Vereadora Rafaela de Nilda:
- Gabinete: Câmara Municipal de Parnamirim
- E-mail: <EMAIL>
- Telefone: [inserir telefone]
- Redes sociais: @vereadorarafaela
    `.trim();
  }

  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  private cleanText(text: string): string {
    return text
      // Remover múltiplas quebras de linha
      .replace(/\n{3,}/g, '\n\n')
      // Remover espaços múltiplos
      .replace(/\s{2,}/g, ' ')
      // Remover caracteres de controle, mas preservar acentos e caracteres especiais do português
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
      // Trim
      .trim();
  }

  chunkText(text: string, options: ChunkOptions): string[] {
    const { chunkSize, overlap, preserveParagraphs = true } = options;
    
    console.log(`📦 Dividindo texto em chunks: ${chunkSize} chars, overlap ${overlap}`);

    // Validar parâmetros
    if (chunkSize <= 0) {
      throw new Error('Tamanho do chunk deve ser maior que 0');
    }
    
    if (overlap < 0 || overlap >= chunkSize) {
      throw new Error('Overlap deve ser >= 0 e < chunkSize');
    }

    if (preserveParagraphs) {
      return this.chunkByParagraphs(text, chunkSize, overlap);
    } else {
      return this.chunkBySize(text, chunkSize, overlap);
    }
  }

  private chunkByParagraphs(text: string, chunkSize: number, overlap: number): string[] {
    const paragraphs = text.split(/\n\s*\n/);
    const chunks: string[] = [];
    let currentChunk = '';

    for (const paragraph of paragraphs) {
      const trimmedParagraph = paragraph.trim();
      if (!trimmedParagraph) continue;

      // Se adicionar este parágrafo exceder o tamanho do chunk
      if (currentChunk.length + trimmedParagraph.length + 2 > chunkSize && currentChunk.length > 0) {
        chunks.push(currentChunk.trim());
        
        // Começar novo chunk com overlap
        const overlapText = this.getOverlapText(currentChunk, overlap);
        currentChunk = overlapText + (overlapText ? '\n\n' : '') + trimmedParagraph;
      } else {
        currentChunk += (currentChunk ? '\n\n' : '') + trimmedParagraph;
      }
    }

    // Adicionar último chunk se não estiver vazio
    if (currentChunk.trim()) {
      chunks.push(currentChunk.trim());
    }

    console.log(`✅ Criados ${chunks.length} chunks por parágrafos`);
    return chunks;
  }

  private chunkBySize(text: string, chunkSize: number, overlap: number): string[] {
    const chunks: string[] = [];
    let start = 0;

    while (start < text.length) {
      let end = Math.min(start + chunkSize, text.length);
      
      // Se não é o último chunk, tentar quebrar em uma palavra
      if (end < text.length) {
        const lastSpace = text.lastIndexOf(' ', end);
        if (lastSpace > start) {
          end = lastSpace;
        }
      }

      const chunk = text.slice(start, end).trim();
      if (chunk) {
        chunks.push(chunk);
      }

      // Calcular próximo início com overlap
      start = Math.max(start + 1, end - overlap);
    }

    console.log(`✅ Criados ${chunks.length} chunks por tamanho`);
    return chunks;
  }

  private getOverlapText(text: string, overlapSize: number): string {
    if (text.length <= overlapSize) return text;
    
    const overlapStart = text.length - overlapSize;
    const overlapText = text.slice(overlapStart);
    
    // Tentar começar em uma palavra completa
    const firstSpace = overlapText.indexOf(' ');
    if (firstSpace > 0 && firstSpace < overlapSize / 2) {
      return overlapText.slice(firstSpace + 1);
    }
    
    return overlapText;
  }

  // Função para extrair metadados do documento
  async extractMetadata(file: File): Promise<DocumentMetadata> {
    try {
      const metadata: DocumentMetadata = {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        uploadDate: new Date().toISOString()
      };

      if (file.type === 'application/pdf') {
        try {
          const arrayBuffer = await file.arrayBuffer();
          const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
          
          metadata.pageCount = pdf.numPages;
          
          // Tentar extrair metadados do PDF
          const pdfMetadata = await pdf.getMetadata();
          if (pdfMetadata.info) {
            // Cast para any para acessar propriedades específicas do PDF
            const info = pdfMetadata.info as any;
            metadata.title = info.Title || undefined;
            metadata.author = info.Author || undefined;
            metadata.subject = info.Subject || undefined;
            metadata.creator = info.Creator || undefined;
            metadata.creationDate = info.CreationDate || undefined;
          }
        } catch (pdfError) {
          console.warn('Erro ao extrair metadados específicos do PDF:', pdfError);
        }
      }

      return metadata;
    } catch (error) {
      console.error('Erro ao extrair metadados:', error);
      return {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        uploadDate: new Date().toISOString()
      };
    }
  }

  // Função para validar arquivo
  validateFile(file: File): FileValidationResult {
    if (!file) {
      return { valid: false, error: 'Arquivo não fornecido' };
    }

    // Verificar tipo
    const allowedTypes = [
      'application/pdf',
      'text/plain',
      'text/markdown',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];

    const allowedExtensions = ['.pdf', '.txt', '.md', '.doc', '.docx'];
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

    if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
      return { valid: false, error: 'Tipo de arquivo não suportado. Use: PDF, TXT, MD, DOC ou DOCX' };
    }

    // Verificar tamanho (máximo 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return { valid: false, error: 'Arquivo muito grande. Máximo 10MB' };
    }

    // Verificar se o arquivo não está vazio
    if (file.size === 0) {
      return { valid: false, error: 'Arquivo está vazio' };
    }

    // Verificar nome
    if (!file.name || file.name.length > 255) {
      return { valid: false, error: 'Nome do arquivo inválido' };
    }

    // Verificar se o nome tem caracteres válidos
    const invalidChars = /[<>:"/\\|?*\x00-\x1f]/;
    if (invalidChars.test(file.name)) {
      return { valid: false, error: 'Nome do arquivo contém caracteres inválidos' };
    }

    return { valid: true };
  }

  // Função para estimar tempo de processamento
  estimateProcessingTime(file: File): number {
    // Estimativa baseada no tamanho do arquivo
    // ~1-2 segundos por MB para PDFs, menos para texto
    const sizeInMB = file.size / (1024 * 1024);
    
    if (file.type === 'application/pdf') {
      return Math.ceil(sizeInMB * 1500); // 1.5 segundos por MB para PDF
    } else {
      return Math.ceil(sizeInMB * 500); // 0.5 segundos por MB para texto
    }
  }

  // Função para extrair texto de diferentes tipos de arquivo
  async extractText(file: File): Promise<string> {
    const validation = this.validateFile(file);
    if (!validation.valid) {
      throw new Error(validation.error);
    }

    switch (file.type) {
      case 'application/pdf':
        return await this.extractTextFromPDF(file);
      
      case 'text/plain':
      case 'text/markdown':
        return await this.extractTextFromTxt(file);
      
      default:
        // Tentar como texto se a extensão for conhecida
        const extension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
        if (['.txt', '.md'].includes(extension)) {
          return await this.extractTextFromTxt(file);
        }
        throw new Error('Tipo de arquivo não suportado para extração de texto');
    }
  }

  private async extractTextFromTxt(file: File): Promise<string> {
    try {
      const text = await file.text();
      return this.cleanText(text);
    } catch (error) {
      console.error('Erro ao extrair texto do arquivo:', error);
      throw new Error('Erro ao ler arquivo de texto');
    }
  }
}

export const documentProcessor = new DocumentProcessor();