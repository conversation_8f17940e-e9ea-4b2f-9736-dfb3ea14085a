import React from 'react';
import { isSupabaseConfigured } from '../config/supabase';
import { isGeminiConfigured } from '../config/gemini';

interface StatusIndicatorProps {
  whatsappConnected?: boolean;
}

export const VereadoraHeader: React.FC<StatusIndicatorProps> = ({ whatsappConnected = false }) => {
  console.log('🔍 VereadoraHeader: whatsappConnected =', whatsappConnected);

  return (
    <header className="bg-white border-b border-gray-200 shadow-sm">
      <div className="max-w-6xl mx-auto px-6 py-6">
        <div className="flex items-center justify-between">
          {/* Logo e Título */}
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg">
              <span className="text-white font-bold text-lg">RN</span>
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-800 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Vereadora Rafaela de Nilda
              </h1>
              <p className="text-base text-gray-600 mt-1">
                Assistente Virtual Inteligente • Parnamirim/RN
              </p>
            </div>
          </div>

          {/* Indicadores de Status */}
          <div className="hidden md:flex items-center space-x-4">
            {/* Sistema */}
            <div className="flex items-center space-x-2">
              <div className={`inline-flex items-center space-x-2 px-3 py-2 rounded-lg border ${
                isSupabaseConfigured && isGeminiConfigured
                  ? 'bg-green-50 border-green-200'
                  : 'bg-yellow-50 border-yellow-200'
              }`}>
                <div className={`w-2 h-2 rounded-full ${
                  isSupabaseConfigured && isGeminiConfigured
                    ? 'bg-green-500'
                    : 'bg-yellow-500'
                }`}></div>
                <span className={`font-medium text-sm ${
                  isSupabaseConfigured && isGeminiConfigured
                    ? 'text-green-800'
                    : 'text-yellow-800'
                }`}>Sistema</span>
              </div>
            </div>

            {/* WhatsApp */}
            <div className="flex items-center space-x-2">
              <div className={`inline-flex items-center space-x-2 px-3 py-2 rounded-lg border ${
                whatsappConnected ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
              }`}>
                <div className={`w-2 h-2 rounded-full ${
                  whatsappConnected ? 'bg-green-500' : 'bg-red-500'
                }`}></div>
                <span className={`font-medium text-sm ${
                  whatsappConnected ? 'text-green-800' : 'text-red-800'
                }`}>WhatsApp</span>
              </div>
            </div>

            {/* Segurança */}
            <div className="flex items-center space-x-2">
              <div className="inline-flex items-center space-x-2 px-3 py-2 rounded-lg border bg-green-50 border-green-200">
                <div className="w-2 h-2 rounded-full bg-green-500"></div>
                <span className="font-medium text-sm text-green-800">Segurança</span>
              </div>
            </div>
          </div>

          {/* Status Mobile */}
          <div className="md:hidden flex items-center space-x-3">
            <div className={`w-3 h-3 rounded-full ${
              isSupabaseConfigured && isGeminiConfigured
                ? 'bg-green-500'
                : 'bg-yellow-500'
            }`}></div>
            <div className={`w-3 h-3 rounded-full ${
              whatsappConnected ? 'bg-green-500' : 'bg-red-500'
            }`}></div>
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
          </div>
        </div>

        {/* Informações de Contato */}
        <div className="mt-6 pt-4 border-t border-gray-200">
          <div className="flex flex-wrap items-center gap-6 text-base text-gray-600">
            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              <span>Câmara Municipal de Parnamirim</span>
            </div>

            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <span><EMAIL></span>
            </div>

            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
              <span>(84) 99999-9999</span>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};
