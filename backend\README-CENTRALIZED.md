# 🏛️ Backend Centralizado - Vereadora Rafaela de Nilda v2.0

Backend Node.js centralizado e otimizado com arquitetura unificada para integração WhatsApp com sistema RAG.

## 🚀 Características da Nova Arquitetura

### ✨ **Servidor Centralizado**
- **Arquitetura Unificada**: Um único ponto de entrada para todos os serviços
- **Gerenciamento de Serviços**: ServiceManager centralizado para coordenar todos os componentes
- **Configuração Centralizada**: Sistema de configuração unificado e validado
- **Roteamento Inteligente**: RouteManager para organizar e proteger endpoints

### 🔧 **Serviços Integrados**
- **WhatsApp Service Factory**: Suporte a múltiplos tipos de serviços (HTTP, Real, Simulator)
- **WPPConnect Server Integrado**: Servidor WPPConnect embutido com fallback automático
- **RAG Service**: Sistema de IA/RAG totalmente integrado
- **Anti-Ban Service**: Proteção avançada contra bloqueios
- **Session Manager**: Gerenciamento robusto de sessões
- **Message Handler**: Processamento inteligente de mensagens

### 🛡️ **Segurança e Monitoramento**
- **Autenticação por API Key**: Sistema de autenticação flexível por roles
- **Rate Limiting**: Proteção contra spam e ataques
- **Health Checks**: Monitoramento contínuo de todos os serviços
- **Logging Avançado**: Sistema de logs estruturado com Winston
- **Error Handling**: Tratamento centralizado de erros

## 📁 Nova Estrutura do Projeto

```
backend/
├── server-centralized.js          # 🎯 Servidor principal centralizado
├── wppconnect-server.js           # 📱 WPPConnect Server integrado
├── package.json                   # 📦 Dependências atualizadas
├── ecosystem.config.cjs           # ⚙️ Configuração PM2 completa
├── .env.example                   # 🔧 Variáveis de ambiente organizadas
│
├── src/
│   ├── config/
│   │   └── ServerConfig.js        # ⚙️ Configuração centralizada
│   │
│   ├── services/
│   │   ├── ServiceManager.js      # 🎛️ Gerenciador de serviços
│   │   ├── WhatsAppServiceFactory.js # 🏭 Factory para serviços WhatsApp
│   │   ├── WhatsAppHttpService.js # 🌐 Serviço HTTP (padrão)
│   │   ├── WhatsAppRealService.js # 📱 Serviço real (WPPConnect direto)
│   │   ├── WhatsAppSimulatorService.js # 🎭 Simulador (desenvolvimento)
│   │   ├── RAGService.js          # 🧠 Serviço de IA/RAG
│   │   ├── MessageHandler.js      # 📨 Processador de mensagens
│   │   ├── SessionManager.js      # 🔐 Gerenciador de sessões
│   │   ├── PersistenceService.js  # 💾 Serviço de persistência
│   │   ├── AntiBanService.js      # 🛡️ Proteção anti-ban
│   │   └── RafaelaResponseService.js # 🏛️ Respostas da Vereadora
│   │
│   ├── routes/
│   │   ├── RouteManager.js        # 🛣️ Gerenciador de rotas
│   │   ├── whatsapp.js           # 📱 Rotas WhatsApp
│   │   ├── session.js            # 🔐 Rotas de sessão
│   │   ├── webhook.js            # 🔗 Rotas de webhook
│   │   ├── health.js             # 💚 Health checks
│   │   ├── security.js           # 🛡️ Rotas de segurança
│   │   ├── antiban.js            # 🚫 Rotas anti-ban
│   │   └── rag.js                # 🧠 Rotas RAG
│   │
│   ├── middleware/
│   │   ├── auth.js               # 🔐 Autenticação
│   │   ├── errorHandler.js       # ❌ Tratamento de erros
│   │   ├── logging.js            # 📝 Middleware de logs
│   │   └── validateWhatsAppSync.js # ✅ Validação de sincronização
│   │
│   └── utils/
│       ├── Logger.js             # 📝 Sistema de logs
│       ├── cache.js              # 🗄️ Sistema de cache
│       ├── languageModel.js      # 🤖 Modelo de linguagem
│       ├── retryHandler.js       # 🔄 Handler de retry
│       └── vectorStore.js        # 🗃️ Vector store
│
├── data/                         # 💾 Dados persistentes
│   ├── sessions/                 # 🔐 Sessões WhatsApp
│   ├── backups/                  # 💾 Backups automáticos
│   └── persistence/              # 📊 Dados de persistência
│
├── logs/                         # 📝 Arquivos de log
├── public/                       # 📁 Arquivos estáticos
└── wppconnect-data/             # 📱 Dados do WPPConnect
    ├── sessions/
    ├── tokens/
    ├── downloads/
    └── uploads/
```

## 🚀 Como Usar

### 1. **Instalação**

```bash
# Instalar dependências
npm install

# Copiar arquivo de configuração
cp .env.example .env

# Editar configurações
nano .env
```

### 2. **Configuração**

Edite o arquivo `.env` com suas configurações:

```bash
# Configurações básicas
PORT=3001
NODE_ENV=development

# Escolha do serviço WhatsApp
WHATSAPP_USE_HTTP=true          # Recomendado para produção
# WHATSAPP_USE_REAL=true        # WPPConnect direto
# WHATSAPP_USE_SIMULATOR=true   # Apenas desenvolvimento

# Configurações do Gemini (opcional)
GEMINI_API_KEY=your-api-key-here

# Configurações do Supabase (opcional)
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-anon-key
```

### 3. **Execução**

#### Desenvolvimento
```bash
# Servidor centralizado
npm run dev
```

#### Produção com PM2
```bash
# Iniciar com PM2
npm run pm2:start

# Verificar status
npm run pm2:logs

# Parar serviços
npm run pm2:stop
```

### 4. **Monitoramento**

```bash
# Status dos serviços
npm run services:status

# Health check
npm run services:health

# Verificar WPPConnect
npm run wppconnect:check
```

## 🔧 Configurações Avançadas

### **Seleção do Serviço WhatsApp**

1. **HTTP Service (Recomendado)**
   ```bash
   WHATSAPP_USE_HTTP=true
   ```
   - Usa WPPConnect Server via HTTP
   - Mais estável e escalável
   - Suporte a Docker

2. **Real Service**
   ```bash
   WHATSAPP_USE_REAL=true
   ```
   - Integração direta com WPPConnect
   - Menor latência
   - Requer mais recursos

3. **Simulator Service**
   ```bash
   WHATSAPP_USE_SIMULATOR=true
   ```
   - Apenas para desenvolvimento
   - Simula comportamento do WhatsApp
   - Não envia mensagens reais

### **Configurações de Segurança**

```bash
# Autenticação
ADMIN_API_KEY=your-admin-key
WHATSAPP_API_KEY=your-whatsapp-key

# Rate limiting
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=60000

# Verificação de origem (produção)
ENABLE_ORIGIN_VERIFICATION=true
```

### **Configurações de Performance**

```bash
# Cache
CACHE_ENABLED=true
CACHE_TTL=3600

# Anti-ban
ANTIBAN_ENABLED=true
ANTIBAN_MIN_DELAY=1000
ANTIBAN_MAX_DELAY=5000

# Monitoramento
MONITORING_ENABLED=true
HEALTH_CHECK_INTERVAL=30000
```

## 📊 Endpoints da API

### **Públicos**
- `GET /` - Informações do servidor
- `GET /api/health` - Health check
- `POST /api/webhook/*` - Webhooks externos
- `POST /api/rag/*` - Sistema RAG

### **Protegidos (API Key)**
- `GET /api/whatsapp/*` - Operações WhatsApp
- `GET /api/session/*` - Gerenciamento de sessões

### **Administrativos (Admin Key)**
- `GET /api/services/*` - Gerenciamento de serviços
- `GET /api/security/*` - Configurações de segurança
- `GET /api/antiban/*` - Configurações anti-ban

## 🔄 Migração Concluída

### **Sistema Centralizado Ativo**
O sistema foi completamente migrado para a arquitetura centralizada:

- ✅ Servidor principal: `server-centralized.js`
- ✅ Servidores legados removidos
- ✅ Scripts atualizados
- ✅ Documentação atualizada

### **Próximos Passos**
1. Configure as variáveis de ambiente no `.env`
2. Teste o servidor centralizado: `npm run dev`
3. Monitore a performance: `npm run services:status`
4. Deploy em produção: `npm run pm2:start`

## 🛠️ Desenvolvimento

### **Adicionando Novos Serviços**
1. Crie o serviço em `src/services/`
2. Adicione ao `ServiceManager.js`
3. Configure no `ServerConfig.js`
4. Adicione rotas no `RouteManager.js`

### **Debugging**
```bash
# Logs detalhados
LOG_LEVEL=debug npm run dev

# Monitoramento de serviços
curl http://localhost:3001/api/services/status
```

## 📈 Benefícios da Centralização

- ✅ **Manutenibilidade**: Código organizado e modular
- ✅ **Escalabilidade**: Fácil adição de novos serviços
- ✅ **Monitoramento**: Visibilidade completa do sistema
- ✅ **Segurança**: Controle centralizado de acesso
- ✅ **Performance**: Otimizações e cache inteligente
- ✅ **Confiabilidade**: Tratamento robusto de erros
- ✅ **Flexibilidade**: Múltiplas opções de configuração
