
import express from 'express';

const router = express.Router();

router.post('/query', async (req, res) => {
  try {
    const { message } = req.body;

    // Usar o RAG Service injetado pelo middleware
    const ragService = req.ragService;
    if (!ragService) {
      return res.status(500).json({
        error: 'Serviço RAG não disponível',
        response: 'O<PERSON><PERSON>, querido(a)! Conte comigo para o que precisar. Estamos juntos por Parnamirim! 🙏🏽',
        sources: []
      });
    }

    const response = await ragService.processMessage(message);
    res.json(response);
  } catch (error) {
    console.error('❌ Erro no RAG query:', error);
    res.status(500).json({
      error: error.message,
      response: 'Ol<PERSON>, querido(a)! Conte comigo para o que precisar. Estamos juntos por Parnamirim! 🙏🏽',
      sources: []
    });
  }
});

export default router;