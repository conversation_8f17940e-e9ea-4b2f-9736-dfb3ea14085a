import { GoogleGenerativeAI } from '@google/generative-ai';
import { Logger } from '../utils/Logger.js';

/**
 * Serviço Avançado do Gemini AI
 * Configurações otimizadas para a Vereadora Rafaela de Nilda
 */
export class GeminiService {
  constructor(config) {
    this.config = config;
    this.logger = new Logger();
    this.genAI = null;
    this.model = null;
    this.isInitialized = false;
    
    // Cache de respostas para otimização
    this.responseCache = new Map();
    this.maxCacheSize = 100;
    
    // Configurações avançadas
    this.generationConfig = {
      temperature: 0.8,
      topK: 40,
      topP: 0.95,
      maxOutputTokens: 1024,
      responseMimeType: "text/plain",
    };
    
    // Safety settings otimizadas
    this.safetySettings = [
      {
        category: "HARM_CATEGORY_HARASSMENT",
        threshold: "BLOCK_MEDIUM_AND_ABOVE",
      },
      {
        category: "HARM_CATEGORY_HATE_SPEECH",
        threshold: "BLOCK_MEDIUM_AND_ABOVE",
      },
      {
        category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
        threshold: "BLOCK_MEDIUM_AND_ABOVE",
      },
      {
        category: "HARM_CATEGORY_DANGEROUS_CONTENT",
        threshold: "BLOCK_MEDIUM_AND_ABOVE",
      },
    ];
  }

  async initialize() {
    try {
      this.logger.info('🧠 Inicializando Gemini AI Avançado...');
      
      if (!this.config.gemini.apiKey) {
        throw new Error('GEMINI_API_KEY não configurada');
      }

      this.genAI = new GoogleGenerativeAI(this.config.gemini.apiKey);
      
      // Usar modelo mais avançado se disponível
      const modelName = this.config.gemini.model || 'gemini-1.5-flash';
      
      this.model = this.genAI.getGenerativeModel({
        model: modelName,
        generationConfig: this.generationConfig,
        safetySettings: this.safetySettings,
      });

      // Testar conexão
      await this.testConnection();
      
      this.isInitialized = true;
      this.logger.info(`✅ Gemini AI inicializado com modelo: ${modelName}`);
      
      return true;
    } catch (error) {
      this.logger.error('❌ Erro ao inicializar Gemini AI:', error);
      this.isInitialized = false;
      return false;
    }
  }

  async testConnection() {
    try {
      const testPrompt = "Responda apenas 'OK' se você está funcionando.";
      const result = await this.model.generateContent(testPrompt);
      const response = result.response.text();
      
      if (!response) {
        throw new Error('Resposta vazia do Gemini');
      }
      
      this.logger.info('✅ Conexão com Gemini AI testada com sucesso');
    } catch (error) {
      throw new Error(`Falha no teste de conexão: ${error.message}`);
    }
  }

  /**
   * Gerar resposta da Vereadora Rafaela com contexto avançado
   */
  async generateRafaelaResponse(userMessage, context = {}) {
    try {
      if (!this.isInitialized) {
        throw new Error('Gemini AI não inicializado');
      }

      // Verificar cache primeiro
      const cacheKey = this.generateCacheKey(userMessage, context);
      if (this.responseCache.has(cacheKey)) {
        this.logger.info('📋 Resposta recuperada do cache');
        return this.responseCache.get(cacheKey);
      }

      // Construir prompt avançado
      const prompt = this.buildAdvancedPrompt(userMessage, context);
      
      this.logger.info('🧠 Gerando resposta com Gemini AI...');
      const startTime = Date.now();
      
      const result = await this.model.generateContent(prompt);
      const response = result.response.text();
      
      const processingTime = Date.now() - startTime;
      
      // Processar e validar resposta
      const processedResponse = this.processResponse(response);
      
      // Salvar no cache
      this.saveToCache(cacheKey, processedResponse);
      
      this.logger.info(`✅ Resposta gerada em ${processingTime}ms`);
      
      return {
        response: processedResponse,
        metadata: {
          model: this.config.gemini.model,
          processingTime,
          cached: false,
          timestamp: new Date().toISOString()
        }
      };
      
    } catch (error) {
      this.logger.error('❌ Erro ao gerar resposta:', error);
      throw error;
    }
  }

  /**
   * Construir prompt avançado com contexto
   */
  buildAdvancedPrompt(userMessage, context) {
    const basePrompt = this.getBasePersonaPrompt();
    const contextPrompt = this.buildContextPrompt(context);
    const instructionsPrompt = this.getInstructionsPrompt(context);
    
    return `${basePrompt}

${contextPrompt}

${instructionsPrompt}

MENSAGEM DO CIDADÃO: "${userMessage}"

RESPOSTA DA VEREADORA RAFAELA:`;
  }

  /**
   * Prompt base da personalidade da Vereadora Rafaela
   */
  getBasePersonaPrompt() {
    return `Você é a Vereadora Rafaela de Nilda, eleita democraticamente para representar o povo de Parnamirim/RN no período 2025-2028.

INFORMAÇÕES PESSOAIS:
- Nome: Vereadora Rafaela de Nilda
- Município: Parnamirim/RN
- Mandato: 2025-2028
- Partido: SOLIDARIEDADE
- Cargo: Vereadora Municipal

PERSONALIDADE E VALORES:
- Carinhosa, atenciosa e genuinamente próxima do povo
- Linguagem acessível, calorosa e humana
- Sempre prestativa e solicita para ajudar
- Comprometida com a transparência e honestidade
- Focada no bem-estar da população de Parnamirim
- Defensora dos direitos sociais e da justiça

ESTILO DE COMUNICAÇÃO:
- Use emojis apropriados: 🙏🏽💖🤩😍👏🏛️🌟
- Linguagem informal mas respeitosa
- Respostas concisas (máximo 2 parágrafos para WhatsApp)
- Tom caloroso e acolhedor
- Sempre ofereça ajuda concreta quando possível

ÁREAS DE ATUAÇÃO PRIORITÁRIAS:
- Saúde pública e bem-estar social
- Educação e oportunidades para jovens
- Infraestrutura urbana e mobilidade
- Segurança pública e iluminação
- Apoio a mulheres e famílias
- Desenvolvimento econômico local
- Meio ambiente e sustentabilidade`;
  }

  /**
   * Construir prompt de contexto baseado na conversa
   */
  buildContextPrompt(context) {
    let contextPrompt = '';
    
    if (context.phoneNumber) {
      contextPrompt += `\nCONTEXTO DA CONVERSA:
- Cidadão: ${context.phoneNumber}`;
    }
    
    if (context.previousMessages && context.previousMessages.length > 0) {
      contextPrompt += `\n- Mensagens anteriores: ${context.previousMessages.length}`;
      
      // Incluir últimas 3 mensagens para contexto
      const recentMessages = context.previousMessages.slice(-3);
      contextPrompt += '\n- Histórico recente:';
      recentMessages.forEach((msg, index) => {
        contextPrompt += `\n  ${index + 1}. ${msg.role}: "${msg.content}"`;
      });
    }
    
    if (context.timeOfDay) {
      const hour = new Date().getHours();
      let greeting = '';
      if (hour < 12) greeting = 'Bom dia';
      else if (hour < 18) greeting = 'Boa tarde';
      else greeting = 'Boa noite';
      
      contextPrompt += `\n- Saudação apropriada: ${greeting}`;
    }
    
    if (context.userLocation && context.userLocation.includes('Parnamirim')) {
      contextPrompt += '\n- Cidadão confirmado de Parnamirim';
    }
    
    return contextPrompt;
  }

  /**
   * Instruções específicas baseadas no contexto
   */
  getInstructionsPrompt(context) {
    let instructions = `
INSTRUÇÕES ESPECÍFICAS:
1. Responda como se fosse a própria Vereadora Rafaela
2. Seja genuinamente calorosa e próxima do povo
3. Use linguagem simples e acessível
4. Inclua emojis apropriados naturalmente
5. Mantenha respostas concisas para WhatsApp (máximo 2 parágrafos)
6. Se não souber algo específico, seja honesta mas mantenha-se disponível
7. Sempre termine oferecendo ajuda adicional
8. Foque em soluções práticas para Parnamirim`;

    // Instruções específicas por tipo de mensagem
    if (context.messageType === 'greeting') {
      instructions += '\n9. Responda à saudação de forma calorosa e pergunte como pode ajudar';
    } else if (context.messageType === 'complaint') {
      instructions += '\n9. Demonstre empatia e ofereça encaminhamento concreto';
    } else if (context.messageType === 'request') {
      instructions += '\n9. Seja específica sobre como pode ajudar e próximos passos';
    } else if (context.messageType === 'question') {
      instructions += '\n9. Responda de forma educativa e ofereça informações adicionais';
    }

    return instructions;
  }

  /**
   * Processar e validar resposta do Gemini
   */
  processResponse(response) {
    if (!response) {
      throw new Error('Resposta vazia do Gemini');
    }

    // Limpar resposta
    let processed = response.trim();
    
    // Remover possíveis prefixos indesejados
    processed = processed.replace(/^(Resposta:|Vereadora Rafaela:|Rafaela:)/i, '').trim();
    
    // Garantir que não seja muito longa para WhatsApp
    if (processed.length > 1000) {
      const sentences = processed.split('. ');
      processed = sentences.slice(0, 3).join('. ');
      if (!processed.endsWith('.')) processed += '.';
    }
    
    // Garantir que tenha pelo menos um emoji se apropriado
    if (!processed.match(/[🙏🏽💖🤩😍👏🏛️🌟]/)) {
      processed += ' 🙏🏽';
    }
    
    return processed;
  }

  /**
   * Gerar chave de cache
   */
  generateCacheKey(userMessage, context) {
    const contextStr = JSON.stringify({
      messageType: context.messageType,
      hasHistory: !!context.previousMessages?.length
    });
    return `${userMessage.toLowerCase().substring(0, 50)}_${contextStr}`;
  }

  /**
   * Salvar resposta no cache
   */
  saveToCache(key, response) {
    if (this.responseCache.size >= this.maxCacheSize) {
      // Remover entrada mais antiga
      const firstKey = this.responseCache.keys().next().value;
      this.responseCache.delete(firstKey);
    }
    
    this.responseCache.set(key, response);
  }

  /**
   * Analisar sentimento da mensagem
   */
  async analyzeSentiment(message) {
    try {
      const prompt = `Analise o sentimento desta mensagem e classifique como: positivo, neutro, negativo, urgente, ou reclamação.
      
Mensagem: "${message}"

Responda apenas com uma palavra: positivo, neutro, negativo, urgente, ou reclamacao`;

      const result = await this.model.generateContent(prompt);
      const sentiment = result.response.text().toLowerCase().trim();
      
      return sentiment;
    } catch (error) {
      this.logger.warn('⚠️ Erro na análise de sentimento:', error);
      return 'neutro';
    }
  }

  /**
   * Detectar tipo de mensagem
   */
  detectMessageType(message) {
    const lowerMessage = message.toLowerCase();
    
    // Saudações
    if (lowerMessage.match(/\b(oi|olá|ola|bom dia|boa tarde|boa noite|hey|e aí)\b/)) {
      return 'greeting';
    }
    
    // Reclamações
    if (lowerMessage.match(/\b(problema|reclamação|reclamacao|ruim|péssimo|pessimo|não funciona|nao funciona)\b/)) {
      return 'complaint';
    }
    
    // Solicitações
    if (lowerMessage.match(/\b(preciso|quero|gostaria|pode|consegue|ajuda|solicito)\b/)) {
      return 'request';
    }
    
    // Perguntas
    if (lowerMessage.match(/\b(como|quando|onde|por que|porque|qual|quais|que horas)\b/) || message.includes('?')) {
      return 'question';
    }
    
    return 'general';
  }

  /**
   * Health check do serviço
   */
  async healthCheck() {
    try {
      if (!this.isInitialized) {
        return { status: 'offline', error: 'Não inicializado' };
      }
      
      // Teste rápido
      const testResult = await this.model.generateContent('Responda apenas "OK"');
      const response = testResult.response.text();
      
      return {
        status: 'online',
        model: this.config.gemini.model,
        cacheSize: this.responseCache.size,
        lastTest: new Date().toISOString(),
        responseTime: response ? 'normal' : 'slow'
      };
    } catch (error) {
      return {
        status: 'error',
        error: error.message,
        lastTest: new Date().toISOString()
      };
    }
  }

  /**
   * Limpar cache
   */
  clearCache() {
    this.responseCache.clear();
    this.logger.info('🗑️ Cache do Gemini limpo');
  }

  /**
   * Obter estatísticas
   */
  getStats() {
    return {
      initialized: this.isInitialized,
      model: this.config.gemini.model,
      cacheSize: this.responseCache.size,
      maxCacheSize: this.maxCacheSize,
      generationConfig: this.generationConfig
    };
  }
}
