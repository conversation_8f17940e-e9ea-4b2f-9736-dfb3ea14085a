{"name": "vereadora-rafaela-backend", "version": "2.0.0", "description": "Backend Centralizado WhatsApp para Assistente Virtual da Vereadora Rafaela de Nilda", "main": "server-centralized.js", "type": "module", "scripts": {"start": "node server-centralized.js", "dev": "node --watch server-centralized.js", "test": "node scripts/test-centralized-server.js", "services:status": "curl -s http://localhost:3001/api/services/status | jq", "services:health": "curl -s http://localhost:3001/api/services/health | jq", "validate": "node --check server-centralized.js && echo 'Sintaxe OK'", "setup": "cp .env.example .env && npm install && echo 'Setup concluído'"}, "keywords": ["whatsapp", "wppconnect", "chatbot", "vereadora", "parnam<PERSON>m", "rag", "ai", "centralized", "backend"], "author": "Sistema RAG Vereadora Rafaela de Nilda", "license": "MIT", "dependencies": {"@google/generative-ai": "^0.1.3", "@supabase/supabase-js": "^2.38.0", "@wppconnect-team/wppconnect": "^1.30.0", "axios": "^1.6.0", "compression": "^1.7.4", "concurrently": "^8.2.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "form-data": "^4.0.3", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mammoth": "^1.6.0", "moment": "^2.29.4", "morgan": "^1.10.0", "multer": "^2.0.1", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "pdf-parse": "^1.1.1", "pdf-poppler": "^0.2.1", "pdf2pic": "^3.2.0", "qrcode": "^1.5.3", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-transport": "^4.6.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/ItaloCabral1995RN/AISTUDIOCHATRAG.git"}, "bugs": {"url": "https://github.com/ItaloCabral1995RN/AISTUDIOCHATRAG/issues"}, "homepage": "https://github.com/ItaloCabral1995RN/AISTUDIOCHATRAG#readme"}