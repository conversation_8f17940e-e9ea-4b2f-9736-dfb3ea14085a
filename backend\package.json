{"name": "vereadora-rafaela-backend", "version": "2.0.0", "description": "Backend Centralizado WhatsApp para Assistente Virtual da Vereadora Rafaela de Nilda", "main": "server-centralized.js", "type": "module", "scripts": {"start": "node server-centralized.js", "dev": "node --watch server-centralized.js", "test": "node scripts/test-centralized-server.js", "migrate": "node scripts/migrate-to-centralized.js", "migrate:rollback": "node scripts/migrate-to-centralized.js --rollback", "pm2:start": "pm2 start ecosystem.config.cjs", "pm2:stop": "pm2 stop ecosystem.config.cjs", "pm2:restart": "pm2 restart ecosystem.config.cjs", "pm2:logs": "pm2 logs", "wppconnect:start": "node start-wppconnect.js", "wppconnect:check": "node scripts/check-wppconnect.js", "services:status": "curl -s http://localhost:3001/api/services/status | jq", "services:health": "curl -s http://localhost:3001/api/services/health | jq", "validate": "node --check server-centralized.js && echo 'Sintaxe OK'", "setup": "cp .env.example .env && npm install && echo 'Setup concluído'"}, "keywords": ["whatsapp", "wppconnect", "chatbot", "vereadora", "parnam<PERSON>m", "rag", "ai", "centralized", "backend"], "author": "Sistema RAG Vereadora Rafaela de Nilda", "license": "MIT", "dependencies": {"axios": "^1.6.0", "cors": "^2.8.5", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "winston": "^3.11.0", "winston-transport": "^4.6.0", "moment": "^2.29.4", "node-cron": "^3.0.3", "jsonwebtoken": "^9.0.2", "qrcode": "^1.5.3", "@supabase/supabase-js": "^2.38.0", "@google/generative-ai": "^0.1.3", "@wppconnect-team/wppconnect": "^1.30.0", "lodash": "^4.17.21", "uuid": "^9.0.1", "concurrently": "^8.2.2", "pdf-parse": "^1.1.1", "mammoth": "^1.6.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/ItaloCabral1995RN/AISTUDIOCHATRAG.git"}, "bugs": {"url": "https://github.com/ItaloCabral1995RN/AISTUDIOCHATRAG/issues"}, "homepage": "https://github.com/ItaloCabral1995RN/AISTUDIOCHATRAG#readme"}