import { supabase, SUPABASE_CONFIG, isSupabaseConfigured } from '../config/supabase';
import { localStorageService } from './localStorageService';
import type { Message, Document, Conversation, DocumentChunk, InteractionLog } from '../types';

class DatabaseService {
  constructor() {
    // Corrigir embeddings no localStorage na inicialização
    this.fixEmbeddingsOnStartup();
  }

  private async fixEmbeddingsOnStartup(): Promise<void> {
    try {
      // Só executar se não estivermos usando Supabase
      if (!isSupabaseConfigured) {
        await localStorageService.fixEmbeddingsInStorage();
      }
    } catch (error) {
      console.warn('⚠️ Erro ao corrigir embeddings na inicialização:', error);
    }
  }

  // Conversas
  async createConversation(title: string, messages: Message[] = []): Promise<Conversation> {
    // Usar localStorage se Supabase não estiver configurado
    if (!isSupabaseConfigured) {
      return localStorageService.createConversation(title, messages);
    }

    const { data, error } = await supabase!
      .from(SUPABASE_CONFIG.tables.conversations)
      .insert({
        title,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        message_count: messages.length
      })
      .select()
      .single();

    if (error) throw error;

    const conversation: Conversation = {
      id: data.id,
      title: data.title,
      created_at: new Date(data.created_at),
      updated_at: new Date(data.updated_at),
      messages: [],
      message_count: data.message_count
    };

    // Adicionar mensagens se fornecidas
    for (const message of messages) {
      await this.addMessageToConversation(conversation.id, message);
    }

    conversation.messages = messages;
    return conversation;
  }

  async getConversations(): Promise<Conversation[]> {
    // Usar localStorage se Supabase não estiver configurado
    if (!isSupabaseConfigured) {
      return localStorageService.getConversations();
    }

    const { data, error } = await supabase!
      .from(SUPABASE_CONFIG.tables.conversations)
      .select('*')
      .order('updated_at', { ascending: false });

    if (error) throw error;

    return data.map(conv => ({
      id: conv.id,
      title: conv.title,
      created_at: new Date(conv.created_at),
      updated_at: new Date(conv.updated_at),
      messages: [],
      message_count: conv.message_count
    }));
  }

  async getConversation(id: string): Promise<Conversation | null> {
    // Usar localStorage se Supabase não estiver configurado
    if (!isSupabaseConfigured) {
      return localStorageService.getConversation(id);
    }

    const { data: convData, error: convError } = await supabase!
      .from(SUPABASE_CONFIG.tables.conversations)
      .select('*')
      .eq('id', id)
      .single();

    if (convError) throw convError;
    if (!convData) return null;

    const { data: messagesData, error: messagesError } = await supabase!
      .from(SUPABASE_CONFIG.tables.messages)
      .select('*')
      .eq('conversation_id', id)
      .order('timestamp', { ascending: true });

    if (messagesError) throw messagesError;

    const messages: Message[] = messagesData.map(msg => ({
      id: msg.id,
      content: msg.content,
      sender: msg.sender,
      timestamp: new Date(msg.timestamp),
      sources: msg.sources || undefined,
      confidence: msg.confidence || undefined
    }));

    return {
      id: convData.id,
      title: convData.title,
      created_at: new Date(convData.created_at),
      updated_at: new Date(convData.updated_at),
      messages,
      message_count: convData.message_count
    };
  }

  async addMessageToConversation(conversationId: string, message: Message): Promise<void> {
    // Usar localStorage se Supabase não estiver configurado
    if (!isSupabaseConfigured) {
      await localStorageService.addMessageToConversation(conversationId, message);
      return;
    }

    const { error } = await supabase!
      .from(SUPABASE_CONFIG.tables.messages)
      .insert({
        id: message.id,
        conversation_id: conversationId,
        content: message.content,
        sender: message.sender,
        timestamp: message.timestamp.toISOString(),
        sources: message.sources || null,
        confidence: message.confidence || null
      });

    if (error) throw error;

    // Atualizar contador de mensagens
    await supabase!
      .from(SUPABASE_CONFIG.tables.conversations)
      .update({
        updated_at: new Date().toISOString(),
        message_count: await this.getMessageCount(conversationId)
      })
      .eq('id', conversationId);
  }

  async deleteConversation(id: string): Promise<void> {
    // Deletar mensagens primeiro
    await supabase
      .from(SUPABASE_CONFIG.tables.messages)
      .delete()
      .eq('conversation_id', id);

    // Deletar conversa
    const { error } = await supabase
      .from(SUPABASE_CONFIG.tables.conversations)
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  private async getMessageCount(conversationId: string): Promise<number> {
    const { count, error } = await supabase
      .from(SUPABASE_CONFIG.tables.messages)
      .select('*', { count: 'exact', head: true })
      .eq('conversation_id', conversationId);

    if (error) throw error;
    return count || 0;
  }

  // Documentos
  async saveDocument(document: Omit<Document, 'id'>): Promise<Document> {
    // Usar localStorage se Supabase não estiver configurado
    if (!isSupabaseConfigured) {
      return localStorageService.saveDocument({
        ...document,
        created_at: document.upload_date
      });
    }

    const { data, error } = await supabase!
      .from(SUPABASE_CONFIG.tables.documents)
      .insert({
        name: document.name,
        type: document.type,
        size: document.size,
        upload_date: document.upload_date.toISOString(),
        status: document.status,
        chunks_count: document.chunks_count || 0
      })
      .select()
      .single();

    if (error) throw error;

    return {
      id: data.id,
      name: data.name,
      type: data.type,
      size: data.size,
      upload_date: new Date(data.upload_date),
      status: data.status,
      chunks_count: data.chunks_count
    };
  }

  async getDocuments(): Promise<Document[]> {
    // Sempre usar localStorage para evitar problemas de conectividade
    try {
      return localStorageService.getDocuments();
    } catch (error) {
      console.error('Erro ao buscar documentos do localStorage:', error);
      return [];
    }
  }

  async updateDocumentStatus(id: string, status: Document['status'], chunksCount?: number): Promise<void> {
    const updateData: any = { status };
    if (chunksCount !== undefined) {
      updateData.chunks_count = chunksCount;
    }

    const { error } = await supabase
      .from(SUPABASE_CONFIG.tables.documents)
      .update(updateData)
      .eq('id', id);

    if (error) throw error;
  }

  async deleteDocument(id: string): Promise<void> {
    // Sempre usar localStorage para evitar problemas de conectividade
    try {
      await localStorageService.deleteDocument(id);
    } catch (error) {
      console.error('Erro ao deletar documento do localStorage:', error);
      throw error;
    }
  }

  // Chunks de documentos
  async saveDocumentChunks(chunks: Omit<DocumentChunk, 'id'>[]): Promise<void> {
    // Sempre usar localStorage para evitar problemas de conectividade
    try {
      console.log(`💾 Salvando ${chunks.length} chunks no localStorage`);
      await localStorageService.saveDocumentChunks(chunks);
      console.log(`✅ ${chunks.length} chunks salvos com sucesso no localStorage`);

      // Log dos primeiros chunks para debug
      if (chunks.length > 0) {
        console.log(`📄 Primeiro chunk: "${chunks[0].content.substring(0, 100)}..."`);
      }
    } catch (error) {
      console.error('❌ Erro ao salvar chunks no localStorage:', error);
      throw error;
    }
  }

  async searchSimilarChunks(embedding: number[], limit: number = 5): Promise<DocumentChunk[]> {
    // Sempre usar localStorage para evitar problemas de conectividade
    try {
      return localStorageService.searchDocumentChunks('', limit);
    } catch (error) {
      console.error('Erro ao buscar chunks do localStorage:', error);
      return [];
    }

    return data.map(chunk => ({
      id: chunk.id,
      document_id: chunk.document_id,
      content: chunk.content,
      chunk_index: chunk.chunk_index,
      embedding: chunk.embedding,
      metadata: {
        ...chunk.metadata,
        document_name: chunk.documents?.name
      }
    }));
  }

  async getDocumentChunks(documentId?: string): Promise<DocumentChunk[]> {
    try {
      console.log('🔍 DatabaseService: Buscando chunks do backend...');

      // Tentar buscar do backend primeiro
      const response = await fetch('http://localhost:3001/api/documents/chunks');
      if (response.ok) {
        const data = await response.json();
        console.log(`📊 DatabaseService: ${data.chunks?.length || 0} chunks encontrados no backend`);

        if (data.chunks && Array.isArray(data.chunks)) {
          // Filtrar por documentId se fornecido
          const chunks = documentId
            ? data.chunks.filter((chunk: any) => chunk.document_id === documentId)
            : data.chunks;

          // Converter para o formato esperado
          return chunks.map((chunk: any) => ({
            id: chunk.id || `chunk_${chunk.document_id}_${chunk.chunk_index}`,
            document_id: chunk.document_id,
            content: chunk.content,
            chunk_index: chunk.chunk_index,
            embedding: chunk.embedding,
            metadata: chunk.metadata || {}
          }));
        }
      }

      console.log('⚠️ DatabaseService: Backend não disponível, usando localStorage...');
      // Fallback para localStorage
      return localStorageService.getDocumentChunks(documentId);
    } catch (error) {
      console.error('❌ DatabaseService: Erro ao buscar chunks:', error);
      // Fallback para localStorage em caso de erro
      try {
        return localStorageService.getDocumentChunks(documentId);
      } catch (localError) {
        console.error('❌ DatabaseService: Erro no localStorage também:', localError);
        return [];
      }
    }
  }

  // Logs de interação
  async logInteraction(log: Omit<InteractionLog, 'id'>): Promise<void> {
    // Sempre usar localStorage para evitar problemas de conectividade
    try {
      await localStorageService.logInteraction(log);
    } catch (error) {
      console.error('Erro ao salvar log no localStorage:', error);
      // Não propagar erro para não afetar funcionalidade principal
    }
  }

  async getInteractionLogs(limit: number = 100): Promise<InteractionLog[]> {
    // Sempre usar localStorage para evitar problemas de conectividade
    try {
      return localStorageService.getInteractionLogs(limit);
    } catch (error) {
      console.error('Erro ao buscar logs do localStorage:', error);
      return [];
    }
  }
}

export const databaseService = new DatabaseService();
