# 🏛️ Backend WhatsApp - Vereadora Rafaela de Nilda

Backend Node.js com WPPConnect para integração WhatsApp com o sistema RAG da Vereadora Rafaela de Nilda.

## 🚀 Características

- **WhatsApp Integration**: Conexão completa com WhatsApp via WPPConnect
- **RAG Integration**: Integração com sistema de IA/RAG do frontend
- **Session Management**: Gerenciamento avançado de sessões
- **Message Handling**: Processamento inteligente de mensagens
- **Auto Responses**: Respostas automáticas personalizadas
- **Business Hours**: Controle de horário de atendimento
- **Rate Limiting**: Proteção contra spam
- **Logging**: Sistema completo de logs
- **Health Checks**: Monitoramento de saúde do sistema
- **Webhooks**: Suporte a webhooks para integração externa

## 📋 Pré-requisitos

- Node.js 18+ 
- NPM ou Yarn
- Sistema RAG frontend rodando (opcional)

## 🔧 Instalação

1. **Clone e navegue para o diretório:**
```bash
cd backend
```

2. **Instale as dependências:**
```bash
npm install
```

3. **Configure as variáveis de ambiente:**
```bash
cp .env.example .env
```

4. **Edite o arquivo `.env` com suas configurações:**
```bash
nano .env
```

## ⚙️ Configuração

### Variáveis de Ambiente Principais

```env
# Servidor
PORT=3001
NODE_ENV=development

# WhatsApp
WHATSAPP_SESSION_NAME=vereadora-rafaela
WHATSAPP_AUTO_CLOSE=false

# Frontend/RAG
FRONTEND_URL=http://localhost:3000
RAG_API_URL=http://localhost:3000/api

# Vereadora
VEREADORA_NAME=Rafaela de Nilda
MUNICIPIO=Parnamirim
ESTADO=RN
GABINETE_TELEFONE=(84) 99999-9999

# Horário de Atendimento
HORARIO_INICIO=08:00
HORARIO_FIM=18:00
DIAS_FUNCIONAMENTO=1,2,3,4,5
```

## 🚀 Execução

### Desenvolvimento
```bash
# Iniciar servidor em modo desenvolvimento (com WPPConnect integrado)
npm run dev
```

### Produção
```bash
npm start
```

## 🔧 Troubleshooting

### Problemas Comuns

#### 1. MaxListenersExceededWarning
**Problema:** Warning sobre muitos event listeners
**Solução:** Já corrigido automaticamente no código

#### 2. Erro de Criptografia
**Problema:** "Erro ao salvar chave de criptografia"
**Solução:**
```bash
# Verificar permissões do diretório
chmod 755 data/
mkdir -p data/sessions data/backups
```

#### 3. WPPConnect Server não acessível
**Problema:** "WPPConnect Server não está acessível"
**Solução:** O WPPConnect agora está integrado ao servidor principal. Apenas execute:
```bash
npm start
```

#### 4. Erro de Inicialização
**Problema:** Falha ao inicializar serviços
**Solução:** O servidor agora funciona em modo degradado, verificar logs específicos

### Logs e Monitoramento
```bash
# Ver logs em tempo real
tail -f logs/whatsapp-backend.log

# Ver logs de erro
tail -f logs/error.log

# Verificar saúde do sistema
curl http://localhost:3001/api/health/detailed
```

## 📱 Conexão WhatsApp

1. **Inicie o servidor**
2. **Acesse o QR Code:**
   - Console: Será exibido no terminal
   - Web: `http://localhost:3001/static/qr-code.png`
   - API: `GET /api/whatsapp/qr`

3. **Escaneie com WhatsApp**
4. **Aguarde confirmação de conexão**

## 🔌 API Endpoints

### WhatsApp
- `GET /api/whatsapp/status` - Status da conexão
- `GET /api/whatsapp/qr` - QR Code atual
- `POST /api/whatsapp/send` - Enviar mensagem
- `GET /api/whatsapp/chats` - Listar chats
- `GET /api/whatsapp/contacts` - Listar contatos
- `POST /api/whatsapp/restart` - Reiniciar conexão

### Sessões
- `GET /api/session/list` - Listar sessões
- `GET /api/session/active` - Sessões ativas
- `GET /api/session/:id` - Obter sessão específica
- `DELETE /api/session/:id` - Deletar sessão
- `POST /api/session/backup` - Criar backup

### Health Check
- `GET /api/health` - Status básico
- `GET /api/health/detailed` - Status detalhado
- `GET /api/health/services` - Status dos serviços

### Webhooks
- `POST /api/webhook/whatsapp` - Webhook WhatsApp
- `POST /api/webhook/rag` - Webhook RAG
- `GET /api/webhook/test` - Testar webhook

## 🤖 Comandos WhatsApp

Os usuários podem usar comandos especiais:

- `/help` - Ajuda e comandos disponíveis
- `/info` - Informações sobre a Vereadora
- `/horario` - Horário de atendimento
- `/contato` - Informações de contato
- `/menu` - Menu principal
- `/status` - Status do sistema

## 🎯 Funcionalidades

### Respostas Automáticas
- Integração com sistema RAG para respostas inteligentes
- Fallback para respostas básicas quando RAG não disponível
- Saudações personalizadas
- Respostas baseadas em palavras-chave

### Horário de Atendimento
- Configuração de horário comercial
- Mensagens automáticas fora do horário
- Controle por dias da semana

### Rate Limiting
- Proteção contra spam
- Limite de mensagens por usuário
- Janela de tempo configurável

### Logging
- Logs estruturados com Winston
- Rotação automática de logs
- Diferentes níveis de log
- Logs específicos para WhatsApp e RAG

## 📊 Monitoramento

### Health Checks
```bash
# Status básico
curl http://localhost:3001/api/health

# Status detalhado
curl http://localhost:3001/api/health/detailed

# Status dos serviços
curl http://localhost:3001/api/health/services
```

### Logs
```bash
# Ver logs em tempo real
tail -f logs/whatsapp-backend.log

# Ver apenas erros
tail -f logs/error.log
```

## 🔒 Segurança

- Rate limiting por IP e usuário
- Validação de entrada
- Sanitização de dados
- Headers de segurança com Helmet
- Verificação de assinatura para webhooks

## 🔧 Desenvolvimento

### Estrutura do Projeto
```
backend/
├── src/
│   ├── services/          # Serviços principais
│   │   ├── WhatsAppService.js
│   │   ├── RAGService.js
│   │   ├── MessageHandler.js
│   │   └── SessionManager.js
│   ├── routes/            # Rotas da API
│   │   ├── whatsapp.js
│   │   ├── session.js
│   │   ├── webhook.js
│   │   └── health.js
│   ├── utils/             # Utilitários
│   │   └── Logger.js
│   └── server.js          # Servidor principal
├── data/                  # Dados persistentes
│   ├── sessions/          # Sessões WhatsApp
│   └── backups/           # Backups
├── logs/                  # Arquivos de log
├── public/                # Arquivos estáticos
└── package.json
```

### Adicionando Novos Recursos

1. **Novo Serviço:**
   - Criar arquivo em `src/services/`
   - Importar e inicializar em `server.js`

2. **Nova Rota:**
   - Criar arquivo em `src/routes/`
   - Adicionar ao `server.js`

3. **Novo Comando WhatsApp:**
   - Adicionar ao `MessageHandler.js`
   - Implementar handler correspondente

## 🐛 Troubleshooting

### WhatsApp não conecta
1. Verificar se QR Code está sendo gerado
2. Verificar logs de erro
3. Tentar reiniciar conexão via API
4. Verificar configurações de rede

### RAG não responde
1. Verificar se frontend está rodando
2. Verificar URL de conexão
3. Testar endpoint RAG diretamente
4. Verificar logs de conectividade

### Sessões perdidas
1. Verificar backups automáticos
2. Restaurar de backup se necessário
3. Verificar permissões de arquivo
4. Verificar espaço em disco

## 📝 Logs

### Tipos de Log
- **Info**: Operações normais
- **Warn**: Situações de atenção
- **Error**: Erros que precisam correção
- **Debug**: Informações detalhadas para desenvolvimento

### Localização
- Logs gerais: `logs/whatsapp-backend.log`
- Apenas erros: `logs/error.log`
- Exceções: `logs/exceptions.log`
- Promises rejeitadas: `logs/rejections.log`

## 🔄 Backup e Restore

### Backup Automático
- Configurado via `BACKUP_ENABLED=true`
- Intervalo configurável via `BACKUP_INTERVAL_HOURS`
- Limpeza automática de backups antigos

### Backup Manual
```bash
curl -X POST http://localhost:3001/api/session/backup
```

### Restore
```bash
curl -X POST http://localhost:3001/api/session/backup/restore \
  -H "Content-Type: application/json" \
  -d '{"backupFileName": "sessions_backup_2024-01-01T00-00-00-000Z.json"}'
```

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 📄 Licença

MIT License - veja o arquivo LICENSE para detalhes.

## 🆘 Suporte

Para suporte técnico:
- 📧 Email: <EMAIL>
- 📞 Telefone: (84) 99999-9999
- 🌐 Website: https://rafaeladenilda.com.br

---

🏛️ **Desenvolvido para a Vereadora Rafaela de Nilda**  
📍 Câmara Municipal de Parnamirim/RN
