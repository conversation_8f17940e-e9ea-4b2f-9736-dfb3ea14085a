import fs from 'fs/promises';
import path from 'path';
import { Logger } from './Logger.js';

class VectorStore {
  constructor() {
    this.vectors = new Map(); // Usar Map para melhor performance
    this.embeddings = new Map(); // Cache de embeddings
    this.logger = new Logger();
    this.dataPath = path.join(process.cwd(), 'data', 'vectors.json');
    this.isInitialized = false;

    // Configurações
    this.maxVectors = parseInt(process.env.MAX_VECTORS) || 10000;
    this.embeddingDimension = parseInt(process.env.EMBEDDING_DIMENSION) || 384;
    this.similarityThreshold = parseFloat(process.env.SIMILARITY_THRESHOLD) || 0.7;

    // Não inicializar automaticamente - será feito manualmente quando necessário
  }

  async initialize() {
    try {
      await this.ensureDataDirectory();
      await this.loadVectors();
      this.isInitialized = true;
      this.logger.info('✅ VectorStore inicializado com sucesso');
    } catch (error) {
      this.logger.error('❌ Erro ao inicializar VectorStore:', error);
    }
  }

  async ensureDataDirectory() {
    const dataDir = path.dirname(this.dataPath);
    try {
      await fs.access(dataDir);
    } catch {
      await fs.mkdir(dataDir, { recursive: true });
      this.logger.info('📁 Diretório de dados criado');
    }
  }

  async loadVectors() {
    try {
      const data = await fs.readFile(this.dataPath, 'utf8');
      const vectorData = JSON.parse(data);

      // Converter array para Map para melhor performance
      vectorData.forEach(item => {
        this.vectors.set(item.id, {
          id: item.id,
          content: item.content,
          embedding: new Float32Array(item.embedding),
          metadata: item.metadata,
          timestamp: new Date(item.timestamp)
        });
      });

      this.logger.info(`📚 Carregados ${this.vectors.size} vetores do disco`);
    } catch (error) {
      if (error.code !== 'ENOENT') {
        this.logger.error('❌ Erro ao carregar vetores:', error);
      } else {
        this.logger.info('📝 Arquivo de vetores não encontrado, iniciando com store vazio');
      }
    }
  }

  async saveVectors() {
    try {
      const vectorArray = Array.from(this.vectors.values()).map(item => ({
        id: item.id,
        content: item.content,
        embedding: Array.from(item.embedding),
        metadata: item.metadata,
        timestamp: item.timestamp.toISOString()
      }));

      await fs.writeFile(this.dataPath, JSON.stringify(vectorArray, null, 2));
      this.logger.debug(`💾 Salvos ${vectorArray.length} vetores no disco`);
    } catch (error) {
      this.logger.error('❌ Erro ao salvar vetores:', error);
    }
  }

  async addDocument(content, metadata = {}, embedding = null) {
    try {
      const id = this.generateId(content);

      // Verificar se já existe
      if (this.vectors.has(id)) {
        this.logger.debug(`📄 Documento já existe: ${id}`);
        return id;
      }

      // Gerar embedding se não fornecido
      if (!embedding) {
        embedding = await this.generateEmbedding(content);
      }

      // Verificar limite de vetores
      if (this.vectors.size >= this.maxVectors) {
        await this.cleanupOldVectors();
      }

      const vectorData = {
        id,
        content,
        embedding: new Float32Array(embedding),
        metadata: {
          ...metadata,
          addedAt: new Date().toISOString(),
          contentLength: content.length
        },
        timestamp: new Date()
      };

      this.vectors.set(id, vectorData);

      // Salvar periodicamente (a cada 10 documentos)
      if (this.vectors.size % 10 === 0) {
        await this.saveVectors();
      }

      this.logger.debug(`✅ Documento adicionado: ${id}`);
      return id;
    } catch (error) {
      this.logger.error('❌ Erro ao adicionar documento:', error);
      throw error;
    }
  }

  async similaritySearch(query, k = 3, threshold = null) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      if (this.vectors.size === 0) {
        this.logger.warn('⚠️ Nenhum vetor disponível para busca');
        return [];
      }

      // Gerar embedding da query
      const queryEmbedding = await this.generateEmbedding(query);
      const usedThreshold = threshold || this.similarityThreshold;

      // Calcular similaridades
      const similarities = [];
      for (const [id, vector] of this.vectors) {
        const similarity = this.cosineSimilarity(queryEmbedding, vector.embedding);

        if (similarity >= usedThreshold) {
          similarities.push({
            id,
            similarity,
            pageContent: vector.content,
            metadata: {
              ...vector.metadata,
              similarity,
              id
            }
          });
        }
      }

      // Ordenar por similaridade e retornar top k
      const results = similarities
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, k);

      this.logger.debug(`🔍 Busca retornou ${results.length} resultados para: "${query.substring(0, 50)}..."`);
      return results;
    } catch (error) {
      this.logger.error('❌ Erro na busca por similaridade:', error);
      return [];
    }
  }

  async generateEmbedding(text) {
    try {
      // Cache de embeddings para evitar recálculos
      const cacheKey = this.hashText(text);
      if (this.embeddings.has(cacheKey)) {
        return this.embeddings.get(cacheKey);
      }

      // Implementação simplificada usando TF-IDF
      // Em produção, usar um modelo real como sentence-transformers
      const embedding = this.textToVector(text);

      // Cache o resultado
      this.embeddings.set(cacheKey, embedding);

      // Limitar cache
      if (this.embeddings.size > 1000) {
        const firstKey = this.embeddings.keys().next().value;
        this.embeddings.delete(firstKey);
      }

      return embedding;
    } catch (error) {
      this.logger.error('❌ Erro ao gerar embedding:', error);
      // Retornar vetor zero em caso de erro
      return new Float32Array(this.embeddingDimension);
    }
  }

  textToVector(text) {
    // Implementação simplificada de TF-IDF
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 2);

    const vector = new Float32Array(this.embeddingDimension);

    // Hash das palavras para posições no vetor
    words.forEach(word => {
      const hash = this.hashText(word);
      const index = Math.abs(hash) % this.embeddingDimension;
      vector[index] += 1;
    });

    // Normalizar o vetor
    const magnitude = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));
    if (magnitude > 0) {
      for (let i = 0; i < vector.length; i++) {
        vector[i] /= magnitude;
      }
    }

    return vector;
  }

  cosineSimilarity(vecA, vecB) {
    if (vecA.length !== vecB.length) {
      throw new Error('Vetores devem ter o mesmo tamanho');
    }

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < vecA.length; i++) {
      dotProduct += vecA[i] * vecB[i];
      normA += vecA[i] * vecA[i];
      normB += vecB[i] * vecB[i];
    }

    const magnitude = Math.sqrt(normA) * Math.sqrt(normB);
    return magnitude === 0 ? 0 : dotProduct / magnitude;
  }

  hashText(text) {
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash;
  }

  generateId(content) {
    const hash = this.hashText(content);
    const timestamp = Date.now();
    return `doc_${Math.abs(hash)}_${timestamp}`;
  }

  async cleanupOldVectors() {
    try {
      const vectorArray = Array.from(this.vectors.values());
      vectorArray.sort((a, b) => a.timestamp - b.timestamp);

      // Remover 10% dos vetores mais antigos
      const toRemove = Math.floor(vectorArray.length * 0.1);
      for (let i = 0; i < toRemove; i++) {
        this.vectors.delete(vectorArray[i].id);
      }

      this.logger.info(`🧹 Removidos ${toRemove} vetores antigos`);
    } catch (error) {
      this.logger.error('❌ Erro na limpeza de vetores:', error);
    }
  }

  async removeDocument(id) {
    if (this.vectors.has(id)) {
      this.vectors.delete(id);
      await this.saveVectors();
      this.logger.debug(`🗑️ Documento removido: ${id}`);
      return true;
    }
    return false;
  }

  getStats() {
    return {
      totalVectors: this.vectors.size,
      maxVectors: this.maxVectors,
      embeddingDimension: this.embeddingDimension,
      similarityThreshold: this.similarityThreshold,
      cacheSize: this.embeddings.size,
      isInitialized: this.isInitialized
    };
  }

  async shutdown() {
    try {
      await this.saveVectors();
      this.vectors.clear();
      this.embeddings.clear();
      this.logger.info('🔄 VectorStore finalizado');
    } catch (error) {
      this.logger.error('❌ Erro ao finalizar VectorStore:', error);
    }
  }
}

export default VectorStore;